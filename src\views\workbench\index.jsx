import Breadcrumb from '@/components/Breadcrumb'
import ThousandSeparator from '@/components/ThousandSeparator'
import { Layout } from 'antd'

import { testTable } from '@/api/example-api'
import AddButton from '@/components/Buttons/AddButton'
import ColToggleButton from '@/components/Buttons/ColToggleButton'
import DeleteButton from '@/components/Buttons/DeleteButton'
import { ProTable, TableDropdown } from '@ant-design/pro-components'
import { Space, Tag } from 'antd'
import { useRef } from 'react'

const waitTimePromise = async (time = 100) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(true)
    }, time)
  })
}

const waitTime = async (time = 100) => {
  await waitTimePromise(time)
}

const columns = [
  {
    dataIndex: 'index',
    valueType: 'indexBorder',
    width: 48,
  },
  {
    title: '标题',
    dataIndex: 'title',
    copyable: true,
    ellipsis: true,
    tooltip: 'The title will shrink automatically if it is too long',
    formItemProps: {
      rules: [
        {
          required: true,
          message: 'This field is required',
        },
      ],
    },
  },
  {
    disable: true,
    title: 'Status',
    dataIndex: 'state',
    filters: true,
    onFilter: true,
    ellipsis: true,
    valueType: 'select',
    valueEnum: {
      all: { text: 'Very Long'.repeat(50) },
      open: {
        text: 'Unresolved',
        status: 'Error',
      },
      closed: {
        text: 'Resolved',
        status: 'Success',
        disabled: true,
      },
      processing: {
        text: 'In Progress',
        status: 'Processing',
      },
    },
  },
  {
    disable: true,
    title: 'Labels',
    dataIndex: 'labels',
    renderFormItem: (_, { defaultRender }) => {
      return defaultRender(_)
    },
    render: (_, record) => (
      <Space>
        {record.labels.map(({ name, color }) => (
          <Tag
            color={color}
            key={name}>
            {name}
          </Tag>
        ))}
      </Space>
    ),
  },
  {
    title: 'Creation Time',
    key: 'showTime',
    dataIndex: 'created_at',
    valueType: 'date',
    sorter: true,
    hideInSearch: true,
  },
  {
    title: 'Creation Time',
    dataIndex: 'created_at',
    valueType: 'dateRange',
    hideInTable: true,
    search: {
      transform: (value) => {
        return {
          startTime: value[0],
          endTime: value[1],
        }
      },
    },
  },
  {
    title: 'Actions',
    valueType: 'option',
    key: 'option',
    render: (text, record, _, action) => [
      <a
        key="editable"
        onClick={() => {
          action?.startEditable?.(record.id)
        }}>
        Edit
      </a>,
      <a
        href={record.url}
        target="_blank"
        rel="noopener noreferrer"
        key="view">
        View
      </a>,
      <TableDropdown
        key="actionGroup"
        onSelect={() => action?.reload()}
        menus={[
          { key: 'copy', name: 'Copy' },
          { key: 'delete', name: 'Delete' },
        ]}
      />,
    ],
  },
]

function WorkBench() {
  const actionRef = useRef()
  return (
    <>
      <Breadcrumb />
      <Layout.Content className="overflow-auto">
        <div className="mb-[12px] rounded-[10px] bg-white p-[18px]">
          <div>
            <h1 className="mb-2 text-[20px]/[28px] font-bold">
              <ThousandSeparator value={2850} />
            </h1>
            <span className="text-gray-500">资质已过期</span>
          </div>
        </div>
        <ProTable
          columns={columns}
          actionRef={actionRef}
          request={async (params, sort, filter) => {
            console.log(sort, filter)
            await waitTime(1000)
            const res = await testTable(params)
            return res
          }}
          editable={{
            type: 'multiple',
          }}
          columnsState={{
            persistenceKey: 'pro-table-singe-demos',
            persistenceType: 'localStorage',
            defaultValue: {
              // 某一项固定
              option: { fixed: 'right', disable: true },
            },
          }}
          rowKey="id"
          search={{
            labelWidth: 'auto',
          }}
          options={{
            reload: false,
            density: false,
            setting: {
              children: <ColToggleButton />,
            },
          }}
          form={{
            // Since transform is configured, the submitted parameters are different from the defined ones, so they need to be transformed here
            syncToUrl: (values, type) => {
              if (type === 'get') {
                return {
                  ...values,
                  created_at: [values.startTime, values.endTime],
                }
              }
              return values
            },
          }}
          pagination={{
            size: 'default',
            pageSize: 50,
            showSizeChanger: true,
            showQuickJumper: true,
          }}
          dateFormatter="string"
          headerTitle={
            <Space>
              <AddButton type="primary">新增</AddButton>
              <DeleteButton
                type="primary"
                danger>
                批量删除
              </DeleteButton>
              <p>（表格自动生成搜索表单并分开示例）</p>
            </Space>
          }
        />
      </Layout.Content>
    </>
  )
}

export default WorkBench
