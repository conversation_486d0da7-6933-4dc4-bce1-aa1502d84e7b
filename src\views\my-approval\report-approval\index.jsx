import {
  approveApply,
  getCellApplyInfoDetail,
  rejectApply,
} from '@/api/myApproval'
import Breadcrumb from '@/components/Breadcrumb'
import Content, { ContentTitle } from '@/layout/components/Content'
import { DownloadOutlined, EyeOutlined } from '@ant-design/icons'
import {
  Button,
  Card,
  Descriptions,
  Image,
  Input,
  message,
  Modal,
  Spin,
  Steps,
  Typography,
} from 'antd'
import { useCallback, useEffect, useState } from 'react'
import { useNavigate, useParams } from 'react-router'

const { TextArea } = Input
const { Text } = Typography

const ReportApproval = () => {
  const { id } = useParams()
  const navigate = useNavigate()
  const [detail, setDetail] = useState(null)
  const [loading, setLoading] = useState(true)
  const [actionLoading, setActionLoading] = useState(false)
  const [rejectModalVisible, setRejectModalVisible] = useState(false)
  const [rejectReason, setRejectReason] = useState('')

  const fetchDetail = useCallback(async () => {
    setLoading(true)
    try {
      const res = await getCellApplyInfoDetail(id)
      setDetail(res)
    } catch {
      message.error('获取详情失败')
      navigate(-1)
    } finally {
      setLoading(false)
    }
  }, [id, navigate])

  useEffect(() => {
    fetchDetail()
  }, [fetchDetail])

  const handleApprove = useCallback(async () => {
    setActionLoading(true)
    try {
      await approveApply(id)
      message.success('审核通过成功')
      navigate(-1)
    } catch (error) {
      message.error(`操作失败: ${error.message}`)
    } finally {
      setActionLoading(false)
    }
  }, [id, navigate])

  const handleReject = useCallback(() => {
    setRejectReason('')
    setRejectModalVisible(true)
  }, [])

  const confirmReject = useCallback(async () => {
    if (!rejectReason.trim()) {
      message.warning('请填写驳回原因')
      return
    }
    setActionLoading(true)
    try {
      await rejectApply(id, rejectReason.trim())
      message.success('驳回成功')
      setRejectModalVisible(false)
      navigate(-1)
    } catch (error) {
      message.error(`操作失败: ${error.message}`)
    } finally {
      setActionLoading(false)
    }
  }, [id, navigate, rejectReason])

  // 订单信息数据
  const orderInfoItems = [
    {
      key: 'orderNo',
      label: '订单号',
      children: detail?.orderNo || '-',
    },
    {
      key: 'userName',
      label: '用户名称',
      children: detail?.userName || '-',
    },
    {
      key: 'orderSource',
      label: '订单来源',
      children: detail?.orderSource || '-',
    },
    {
      key: 'orderAmount',
      label: '订单金额',
      children: detail?.orderAmount ? `¥${detail.orderAmount}` : '-',
    },
    {
      key: 'orderTime',
      label: '下单时间',
      children: detail?.orderTime || '-',
    },
  ]

  // 细胞信息数据
  const cellInfoItems = [
    {
      key: 'cellNameCn',
      label: '细胞名称（中文）',
      children: detail?.cellNameCn || '-',
    },
    {
      key: 'cellNameEn',
      label: '细胞名称（英文）',
      children: detail?.cellNameEn || '-',
    },
    {
      key: 'cellSource',
      label: '细胞来源',
      children: detail?.cellSource || '-',
    },
    {
      key: 'sampleTypeAndCount',
      label: '样品类型及数量',
      children: detail?.sampleTypeAndCount || '-',
    },
    {
      key: 'cellTypeAndGeneration',
      label: '细胞类型及代次',
      children: detail?.cellTypeAndGeneration || '-',
    },
    {
      key: 'targetJournal',
      label: '拟投稿期刊',
      children: detail?.targetJournal || '-',
    },
    {
      key: 'supervisorName',
      label: '导师姓名',
      children: detail?.supervisorName || '-',
    },
    {
      key: 'supervisorContact',
      label: '导师联系方式',
      children: detail?.supervisorContact || '-',
    },
    {
      key: 'projectSource',
      label: '课题来源',
      children: detail?.projectSource || '-',
    },
    {
      key: 'projectName',
      label: '课题名称',
      children: detail?.projectName || '-',
      span: 2,
    },
  ]

  // 审核进度步骤
  const getStepStatus = (stepIndex) => {
    const currentStep = detail?.currentStep || 0
    if (stepIndex < currentStep) return 'finish'
    if (stepIndex === currentStep) return 'process'
    return 'wait'
  }

  const stepItems = [
    {
      title: '初审',
      status: getStepStatus(0),
      description: (
        <div className="text-center">
          <div>
            {detail?.firstReviewer ||
              (detail?.currentStep === 0 ? '审核中' : '-')}
          </div>
          <div className="text-xs text-gray-500">
            {detail?.firstReviewTime || ''}
          </div>
        </div>
      ),
    },
    {
      title: '复审',
      status: getStepStatus(1),
      description: (
        <div className="text-center">
          <div>
            {detail?.secondReviewer ||
              (detail?.currentStep === 1 ? '审核中' : '-')}
          </div>
          <div className="text-xs text-gray-500">
            {detail?.secondReviewTime || ''}
          </div>
        </div>
      ),
    },
    {
      title: '用户确认',
      status: getStepStatus(2),
      description: (
        <div className="text-center">
          <div>
            {detail?.userConfirmer ||
              (detail?.currentStep === 2 ? '审核中' : '-')}
          </div>
          <div className="text-xs text-gray-500">
            {detail?.userConfirmTime || ''}
          </div>
        </div>
      ),
    },
  ]

  // 检测信息数据（动态展示）
  const testInfoItems = detail?.testInfo
    ? Object.entries(detail.testInfo).map(([key, value]) => ({
        key,
        label: key,
        children: value || '-',
      }))
    : []

  // 处理文件下载
  const handleDownload = (fileUrl, fileName) => {
    const link = document.createElement('a')
    link.href = fileUrl
    link.download = fileName
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  return (
    <div className="flex h-full flex-col overflow-hidden">
      <Breadcrumb showBackArrow>
        {detail?.status === 0 && (
          <>
            <Button
              type="primary"
              onClick={handleApprove}
              loading={actionLoading}
              className="mr-2">
              通过
            </Button>
            <Button
              onClick={handleReject}
              loading={actionLoading}>
              驳回
            </Button>
          </>
        )}
      </Breadcrumb>

      <Content>
        <Spin spinning={loading}>
          {detail && (
            <div className="space-y-6">
              {/* 订单信息 */}
              <div>
                <ContentTitle title="订单信息" />
                <Descriptions
                  items={orderInfoItems}
                  column={2}
                  bordered
                  size="small"
                />
              </div>

              {/* 审核进度 */}
              <div>
                <ContentTitle title="审核进度" />
                <div className="flex justify-center py-8">
                  <Steps
                    current={detail?.currentStep || 0}
                    items={stepItems}
                    className="w-full max-w-2xl"
                  />
                </div>
              </div>

              {/* 细胞信息 */}
              <div>
                <ContentTitle title="细胞信息" />
                <Descriptions
                  items={cellInfoItems}
                  column={2}
                  bordered
                  size="small"
                />
              </div>

              {/* 检测信息 */}
              {testInfoItems.length > 0 && (
                <div>
                  <ContentTitle title="检测信息" />
                  <Descriptions
                    items={testInfoItems}
                    column={2}
                    bordered
                    size="small"
                  />
                </div>
              )}

              {/* 检测图片 */}
              {detail?.testImages && detail.testImages.length > 0 && (
                <div>
                  <ContentTitle title="检测图片" />
                  <div className="grid grid-cols-2 gap-4 md:grid-cols-3 lg:grid-cols-4">
                    {detail.testImages.map((image, index) => (
                      <div
                        key={index}
                        className="overflow-hidden rounded-lg border">
                        <Image
                          src={image.url}
                          alt={image.name || `检测图片${index + 1}`}
                          className="h-32 w-full object-cover"
                          preview={{
                            mask: <EyeOutlined className="text-white" />,
                          }}
                        />
                        <div className="truncate p-2 text-sm text-gray-600">
                          {image.name || `检测图片${index + 1}`}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* 检测附件 */}
              {detail?.testAttachments && detail.testAttachments.length > 0 && (
                <div>
                  <ContentTitle title="检测附件" />
                  <div className="space-y-2">
                    {detail.testAttachments.map((attachment, index) => (
                      <Card
                        key={index}
                        size="small"
                        className="cursor-pointer transition-shadow hover:shadow-md">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-2">
                            <DownloadOutlined className="text-blue-500" />
                            <Text>{attachment.name}</Text>
                            <Text
                              type="secondary"
                              className="text-sm">
                              ({attachment.size || '未知大小'})
                            </Text>
                          </div>
                          <Button
                            type="link"
                            size="small"
                            onClick={() =>
                              handleDownload(attachment.url, attachment.name)
                            }>
                            查看
                          </Button>
                        </div>
                      </Card>
                    ))}
                  </div>
                </div>
              )}

              {/* 检测报告 */}
              {detail?.testReports && detail.testReports.length > 0 && (
                <div>
                  <ContentTitle title="检测报告" />
                  <div className="space-y-2">
                    {detail.testReports.map((report, index) => (
                      <Card
                        key={index}
                        size="small"
                        className="cursor-pointer transition-shadow hover:shadow-md">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-2">
                            <DownloadOutlined className="text-green-500" />
                            <Text>{report.name}</Text>
                            <Text
                              type="secondary"
                              className="text-sm">
                              ({report.size || '未知大小'})
                            </Text>
                          </div>
                          <Button
                            type="link"
                            size="small"
                            onClick={() =>
                              handleDownload(report.url, report.name)
                            }>
                            查看
                          </Button>
                        </div>
                      </Card>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}
        </Spin>
      </Content>

      {/* 驳回原因弹窗 */}
      <Modal
        title="驳回原因"
        open={rejectModalVisible}
        onOk={confirmReject}
        onCancel={() => setRejectModalVisible(false)}
        okText="确认"
        cancelText="取消"
        confirmLoading={actionLoading}
        width={500}>
        <div className="mb-4">
          <p>请填写驳回原因：</p>
        </div>
        <TextArea
          rows={4}
          value={rejectReason}
          onChange={(e) => setRejectReason(e.target.value)}
          placeholder="请输入驳回原因"
          maxLength={500}
          showCount
        />
      </Modal>
    </div>
  )
}

export default ReportApproval
