import tailwindcss from '@tailwindcss/vite'
import react from '@vitejs/plugin-react'
import { fileURLToPath, URL } from 'node:url'
import { defineConfig } from 'vite'
// 2在维护所以用这个插件
import { visualizer } from 'rollup-plugin-visualizer'
import { compression } from 'vite-plugin-compression2'

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    react(),
    tailwindcss(),
    /**
     * 1、前端压缩好gzip，可以不需要Nginx实时压缩
     * 2、需要Nginx进一步配置才能真正生效（https://doc.ruoyi.vip/ruoyi-vue/other/faq.html#%E4%BD%BF%E7%94%A8gzip%E8%A7%A3%E5%8E%8B%E7%BC%A9%E9%9D%99%E6%80%81%E6%96%87%E4%BB%B6）
     */
    compression(),
    // 打包工具分析
    visualizer({
      filename: 'dist/stats.html',
    }),
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url)),
    },
  },
  // proComponent-Form会循环依赖， 打包到同一个文件
  build: {
    rollupOptions: {
      output: {
        manualChunks(id) {
          if (id.includes('node_modules')) {
            // 让每个插件都打包成独立的文件
            return id
              .toString()
              .split('node_modules/')[1]
              .split('/')[0]
              .toString()
          }
        },
      },
    },
  },
  server: {
    port: 5174,
  },
})
