import request from '@/utils/request/alova'

/**
 * 测试列表接口
 * @param {*} data
 * @returns
 */
export function testTable(data) {
  return request.get('/test-pro-table-list', data, {
    // 常规后端表格数据结构直接传入转换类型交给全局处理
    transformType: 'proTable',
  })
}
/**
 * 测试列表接口
 * @param {*} data
 * @returns
 */
export function testTableOther(data) {
  // 特殊结构自己编写转换器处理为ProTable需要的
  return request.get('/test-pro-table-list-other', data, {
    transform: (res) => {
      return {
        data: res.data.data.tableListData,
        total: res.data.data.total,
        success: true,
      }
    },
  })
}
