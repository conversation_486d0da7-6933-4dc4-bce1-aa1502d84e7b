import { saveDetectionApply } from '@/api/myApplication'
import { getCellApplyInfoDetail } from '@/api/myApproval'
import Breadcrumb from '@/components/Breadcrumb'
import {
  Alert,
  Button,
  Card,
  Checkbox,
  DatePicker,
  Form,
  Input,
  Select,
  message,
} from 'antd'
import dayjs from 'dayjs'
import { useCallback, useEffect, useState } from 'react'
import { useNavigate, useSearchParams } from 'react-router'

const { TextArea } = Input

// 细胞种属选项
const speciesOptions = [
  { label: '人', value: 1 },
  { label: '小鼠', value: 2 },
]

// 细胞类型选项
const cellTypeOptions = [
  { label: '悬浮', value: '悬浮' },
  { label: '贴壁', value: '贴壁' },
]

// 细胞交叉污染检测选项
const crossContaminationOptions = [
  { label: 'STR(只接收来源于人和小鼠的细胞)', value: 1 },
  { label: '同工酶', value: 2 },
  { label: 'PCR种属鉴定', value: 3 },
  { label: 'DNA Barcoding', value: 4 },
]

// 外源污染物检测选项
const exogenousContaminationOptions = [
  { label: '支原体培养法', value: 5 },
  { label: '支原体PCR检测法', value: 6 },
  { label: '支原体DNA染色法', value: 7 },
  { label: '细菌真菌接种法', value: 8 },
]

function QualityInspectionApply() {
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const [messageApi, contextHolder] = message.useMessage()
  const navigate = useNavigate()
  const [searchParams] = useSearchParams()
  const applyBid = searchParams.get('applyBid') // 重新发起申请时的ID

  // 加载申请详情（用于重新发起）
  const loadApplyDetail = useCallback(() => {
    setLoading(true)
    return getCellApplyInfoDetail(applyBid)
      .then((data) => {
        // 处理日期字段
        if (data.receiveTime) {
          data.receiveTime = dayjs(data.receiveTime)
        }
        form.setFieldsValue(data)
      })
      .finally(() => {
        setLoading(false)
      })
  }, [applyBid, form])

  // 初始化表单数据（重新发起申请时）
  useEffect(() => {
    if (applyBid) {
      loadApplyDetail()
    }
  }, [applyBid, loadApplyDetail])

  // 取消操作
  const handleCancel = () => {
    navigate.back()
  }

  // 提交申请
  const handleSubmit = () => {
    form
      .validateFields()
      .then((values) => {
        // 处理日期字段
        if (values.receiveTime) {
          values.receiveTime = values.receiveTime.format('YYYY-MM-DD HH:mm:ss')
        }

        setLoading(true)
        // 构建提交质检申请DTO
        const submitData = {
          ...values,
          applyType: 1, // 质检申请类型
          reApplyBid: applyBid, // 重新发起时的原申请ID
          // 将检测项目数组转换为逗号分隔的字符串
          crossContamination: values.crossContamination?.join(','),
          exogenousContamination: values.exogenousContamination?.join(','),
        }
        return saveDetectionApply(submitData)
      })
      .then(() => {
        messageApi.success('质检申请提交成功')
        navigate('/myApplication')
      })
      .finally(() => {
        setLoading(false)
      })
  }

  return (
    <div className="box-border flex h-full flex-col bg-white p-6">
      {contextHolder}
      <Breadcrumb showBackArrow>
        <Button onClick={handleCancel}>取消</Button>
        <Button
          type="primary"
          loading={loading}
          onClick={handleSubmit}>
          提交申请
        </Button>
      </Breadcrumb>

      <div className="flex-1 space-y-6 overflow-auto">
        {/* 检测时间提示 */}
        <Alert
          message="检测时间提示"
          description={
            <div className="space-y-1">
              <div>
                • STR、PCR种属鉴定、DNA Barcoding、支原体PCR检测法 — 7天
              </div>
              <div>• 支原体DNA染色法 — 14天</div>
              <div>• 支原体培养法、细菌真菌接种法 — 30天</div>
            </div>
          }
          type="info"
          showIcon
        />

        <Form
          form={form}
          scrollToFirstError
          layout="vertical">
          {/* 细胞信息 */}
          <Card
            title="细胞信息"
            className="!mt-6">
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
              <Form.Item
                label="细胞中心编号"
                name="cellCenterCode"
                rules={[{ required: true, message: '请输入细胞中心编号' }]}>
                <Input placeholder="请输入细胞中心编号" />
              </Form.Item>

              <Form.Item
                label="细胞中文名称"
                name="cellChineseName"
                rules={[{ required: true, message: '请输入细胞中文名称' }]}>
                <Input placeholder="请输入细胞中文名称" />
              </Form.Item>

              <Form.Item
                label="细胞英文名称"
                name="cellEnglishName"
                rules={[{ required: true, message: '请输入细胞英文名称' }]}>
                <Input placeholder="请输入细胞英文名称" />
              </Form.Item>

              <Form.Item
                label="细胞种属"
                name="cellSpecies"
                rules={[{ required: true, message: '请选择细胞种属' }]}>
                <Select
                  placeholder="请选择细胞种属"
                  options={speciesOptions}
                />
              </Form.Item>

              <Form.Item
                label="细胞代次"
                name="cellGeneration"
                rules={[{ required: true, message: '请输入细胞代次' }]}>
                <Input placeholder="请输入细胞代次" />
              </Form.Item>

              <Form.Item
                label="细胞类型"
                name="cellType"
                rules={[{ required: true, message: '请选择细胞类型' }]}>
                <Select
                  placeholder="请选择细胞类型"
                  options={cellTypeOptions}
                />
              </Form.Item>

              <Form.Item
                label="细胞来源"
                name="cellSource">
                <Input placeholder="请输入细胞来源" />
              </Form.Item>

              <Form.Item
                label="培养条基"
                name="cultureBase">
                <Input placeholder="请输入培养条基" />
              </Form.Item>

              <Form.Item
                label="培养条件"
                name="cultureCondition">
                <Input placeholder="请输入培养条件" />
              </Form.Item>

              <Form.Item
                label="接收/复苏时间"
                name="receiveTime">
                <DatePicker
                  showTime
                  placeholder="请选择接收/复苏时间"
                  format="YYYY-MM-DD HH:mm:ss"
                  className="w-full"
                />
              </Form.Item>
            </div>

            <Form.Item
              label="备注"
              name="remark">
              <TextArea
                rows={3}
                placeholder="请输入备注信息"
              />
            </Form.Item>
          </Card>

          {/* 质检项信息 */}
          <Card
            title="质检项信息"
            className="!mt-6">
            <div className="space-y-6">
              <Form.Item
                label="细胞交叉污染检测"
                name="crossContamination"
                rules={[
                  {
                    required: true,
                    type: 'array',
                    min: 1,
                    message: '请至少选择一项细胞交叉污染检测',
                  },
                ]}>
                <Checkbox.Group options={crossContaminationOptions} />
              </Form.Item>

              <Form.Item
                label="外源污染物检测"
                name="exogenousContamination"
                rules={[
                  {
                    required: true,
                    type: 'array',
                    min: 1,
                    message: '请至少选择一项外源污染物检测',
                  },
                ]}>
                <Checkbox.Group options={exogenousContaminationOptions} />
              </Form.Item>
            </div>
          </Card>
        </Form>
      </div>
    </div>
  )
}

export default QualityInspectionApply
