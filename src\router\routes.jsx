import PageSpin from '@/components/PageSpin'
import Layout from '@/layout'
import IndexRoute from '@/router/indexRoute'
import { Navigate } from 'react-router'
import Result404 from './404'

/**
 * @type {import('react-router').RouteObject[] }
 */
export const routes = [
  {
    path: '/',
    element: <Layout />,
    handle: () => ({
      title: '首页',
    }),
    // 如果有一个固定首页可以添加一个默认路由
    children: [
      {
        index: true,
        element: <IndexRoute />,
      },
      // 工作台
      {
        path: 'workbench',
        lazy: async () => ({
          Component: (await import('@/views/workbench/index')).default,
        }),
        // 使用了lazy组件就需要
        HydrateFallback: () => <PageSpin />,
        // 自定义信息 matched是可以获取到
        handle: () => ({
          title: '工作台',
          permissions: ['system:workbench'],
        }),
      },
      // 我的申请 - 细胞检测
      {
        path: 'my-application',
        lazy: async () => ({
          Component: (await import('@/views/my-application/index')).default,
        }),
        HydrateFallback: () => <PageSpin />,
        handle: () => ({
          title: '我的申请',
          permissions: ['system:myApplication'],
        }),
        children: [
          // 质检申请
          {
            path: 'quality-apply',
            lazy: async () => ({
              Component: (
                await import('@/views/my-application/QualityInspectionApply')
              ).default,
            }),
            HydrateFallback: () => <PageSpin />,
            handle: () => ({
              title: '质检申请',
              permissions: ['system:myApplication'],
            }),
          },
        ],
      },
      {
        path: 'my-approval',
        lazy: async () => ({
          Component: (await import('@/views/my-approval/index')).default,
        }),
        HydrateFallback: () => <PageSpin />,
        handle: () => ({
          title: '我的审批',
          permissions: ['system:my-approval'],
        }),
        children: [
          {
            path: 'detail/:id',
            lazy: async () => ({
              Component: (await import('@/views/my-approval/detail/index'))
                .default,
            }),
            HydrateFallback: () => <PageSpin />,
            handle: () => ({
              title: '审批详情',
              permissions: ['system:my-approval'],
            }),
          },
          {
            path: 'amplification-outbound/:id',
            lazy: async () => ({
              Component: (
                await import('@/views/my-approval/amplification-outbound/index')
              ).default,
            }),
            HydrateFallback: () => <PageSpin />,
            handle: () => ({
              title: '扩增出库',
              permissions: ['system:my-approval'],
            }),
          },
          {
            path: 'outbound-detail/:id',
            lazy: async () => ({
              Component: (
                await import('@/views/my-approval/outbound-detail/index')
              ).default,
            }),
            HydrateFallback: () => <PageSpin />,
            handle: () => ({
              title: '出库详情',
              permissions: ['system:my-approval'],
            }),
          },
          {
            path: 'report-approval/:id',
            lazy: async () => ({
              Component: (
                await import('@/views/my-approval/report-approval/index')
              ).default,
            }),
            HydrateFallback: () => <PageSpin />,
            handle: () => ({
              title: '报告审批',
              permissions: ['system:my-approval'],
            }),
          },
        ],
      },
      // 示例
      {
        path: 'example-page',
        lazy: async () => ({
          Component: (await import('@/views/example-page/page2')).default,
        }),
        HydrateFallback: () => <PageSpin />,
        handle: () => ({
          title: '示例页面',
          permissions: ['system:example-page'],
        }),
        children: [
          // 有目录的情况添加一个默认路由
          {
            index: true,
            element: (
              <Navigate
                to="list"
                replace
              />
            ),
          },
          {
            path: 'list',
            lazy: async () => ({
              Component: (await import('@/views/example-page/list')).default,
            }),
            HydrateFallback: () => <PageSpin />,
            handle: () => ({
              title: '列表页',
              permissions: ['system:example-page:list'],
            }),
          },
          {
            path: 'form',
            lazy: async () => ({
              Component: (await import('@/views/example-page/form')).default,
            }),
            HydrateFallback: () => <PageSpin />,
            handle: () => ({
              title: '表单页',
              // 不在菜单中的路由要指定激活菜单
              activeMenu: '/example-page/list',
            }),
          },
          {
            path: 'detail/:id',
            lazy: async () => ({
              Component: (await import('@/views/example-page/detail')).default,
            }),
            HydrateFallback: () => <PageSpin />,
            handle: () => ({
              title: '详情页',
              activeMenu: '/example-page/list',
            }),
          },
        ],
      },
    ],
  },
  // 404页面
  { path: '/404', element: <Result404 /> },
  {
    path: '*',
    element: (
      <Navigate
        to="/404"
        replace
      />
    ),
  },
]

// 白名单路由，不需要登录就可以访问的页面列表
export const whiteRoutes = ['/404']
