/**
 * 材料审批模块列配置
 */

// 材料审批基础列配置
export function getMaterialColumns() {
  return [
    {
      title: '订单号',
      dataIndex: 'orderNo',
      key: 'orderNo',
      width: 150,
      hideInTable: false,
      copyable: true,
    },
    {
      title: '类别',
      dataIndex: 'category',
      key: 'category',
      width: 120,
      valueType: 'select',
      valueEnum: {
        cell: { text: '细胞' },
      },
      hideInTable: false,
    },
    {
      title: '业务',
      dataIndex: 'business',
      key: 'business',
      width: 150,
      valueType: 'select',
      request: async () => [
        { label: '业务1', value: 'business1' },
        { label: '业务2', value: 'business2' },
        { label: '业务3', value: 'business3' },
      ],
      hideInTable: false,
    },
    {
      title: '材料',
      dataIndex: 'material',
      key: 'material',
      width: 150,
      valueType: 'select',
      request: async () => [
        { label: '材料1', value: 'material1' },
        { label: '材料2', value: 'material2' },
        { label: '材料3', value: 'material3' },
      ],
      hideInTable: false,
    },
    {
      title: '申请人',
      dataIndex: 'applicant',
      key: 'applicant',
      width: 120,
      hideInTable: false,
    },
    {
      title: '申请时间',
      dataIndex: 'applyTime',
      key: 'applyTime',
      width: 180,
      valueType: 'dateTime',
      hideInTable: false,
      sorter: true,
    },
    {
      title: '审批时间',
      dataIndex: 'approveTime',
      key: 'approveTime',
      width: 180,
      valueType: 'dateTime',
      hideInTable: false,
      hideInSearch: true, // 在待审批页面的搜索中隐藏
      sorter: true,
    },
    {
      title: '订单来源',
      dataIndex: 'orderSource',
      key: 'orderSource',
      width: 120,
      hideInTable: false,
      hideInSearch: true,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      valueType: 'select',
      valueEnum: {
        0: { text: '待审批', status: 'Processing' },
        1: { text: '通过', status: 'Success' },
        2: { text: '驳回', status: 'Error' },
      },
      hideInTable: false,
      hideInSearch: true, // 在搜索中隐藏，通过Tab切换控制
    },
  ]
}

// 获取待审批页面的列配置
export function getMaterialPendingColumns() {
  return getMaterialColumns()
    .map((col) => {
      if (col.key === 'approveTime' || col.key === 'status') {
        return { ...col, hideInTable: true }
      }
      return col
    })
    .filter((col) => !col.hideInTable)
}

// 获取审批记录页面的列配置
export function getMaterialRecordColumns() {
  return getMaterialColumns().map((col) => {
    if (col.key === 'applyTime') {
      return { ...col, hideInSearch: false }
    }
    if (col.key === 'approveTime') {
      return { ...col, hideInSearch: false }
    }
    return col
  })
}

// 获取待审批页面的搜索字段
export function getMaterialPendingSearchFields() {
  return [
    {
      title: '订单号',
      dataIndex: 'orderNo',
      valueType: 'text',
    },
    {
      title: '类别',
      dataIndex: 'category',
      valueType: 'select',
      valueEnum: {
        cell: { text: '细胞' },
      },
    },
    {
      title: '业务',
      dataIndex: 'business',
      valueType: 'select',
      request: async () => [
        { label: '业务1', value: 'business1' },
        { label: '业务2', value: 'business2' },
        { label: '业务3', value: 'business3' },
      ],
    },
    {
      title: '材料',
      dataIndex: 'material',
      valueType: 'select',
      request: async () => [
        { label: '材料1', value: 'material1' },
        { label: '材料2', value: 'material2' },
        { label: '材料3', value: 'material3' },
      ],
    },
    {
      title: '申请人',
      dataIndex: 'applicant',
      valueType: 'text',
    },
    {
      title: '申请时间',
      dataIndex: 'applyTime',
      valueType: 'dateTimeRange',
    },
  ]
}

// 获取审批记录页面的搜索字段
export function getMaterialRecordSearchFields() {
  return [
    {
      title: '订单号',
      dataIndex: 'orderNo',
      valueType: 'text',
    },
    {
      title: '类别',
      dataIndex: 'category',
      valueType: 'select',
      valueEnum: {
        cell: { text: '细胞' },
      },
    },
    {
      title: '业务',
      dataIndex: 'business',
      valueType: 'select',
      request: async () => [
        { label: '业务1', value: 'business1' },
        { label: '业务2', value: 'business2' },
        { label: '业务3', value: 'business3' },
      ],
    },
    {
      title: '材料',
      dataIndex: 'material',
      valueType: 'select',
      request: async () => [
        { label: '材料1', value: 'material1' },
        { label: '材料2', value: 'material2' },
        { label: '材料3', value: 'material3' },
      ],
    },
    {
      title: '申请人',
      dataIndex: 'applicant',
      valueType: 'text',
    },
    {
      title: '审批时间',
      dataIndex: 'approveTime',
      valueType: 'dateTimeRange',
    },
    {
      title: '申请时间',
      dataIndex: 'applyTime',
      valueType: 'dateTimeRange',
    },
  ]
}
