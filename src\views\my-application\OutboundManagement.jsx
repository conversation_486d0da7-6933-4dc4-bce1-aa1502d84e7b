import { selectCellApplyRecordPage } from '@/api/myApproval'
import tableProStyles from '@/assets/styles/ant-pro.module.scss'
import ColToggleButton from '@/components/Buttons/ColToggleButton'
import { useRouter } from '@/hooks/router'
import { ProTable } from '@ant-design/pro-components'
import { Modal, Space, message } from 'antd'
import { useRef } from 'react'

function OutboundManagement() {
  const { push } = useRouter()
  const ref = useRef(null)
  const [messageApi, contextHolder] = message.useMessage()

  // 状态标签渲染
  const renderStatus = (status) => {
    const statusMap = {
      0: { color: 'orange', text: '待审批' },
      1: { color: 'green', text: '通过' },
      2: { color: 'red', text: '驳回' },
      3: { color: 'blue', text: '检测中' },
      4: { color: 'cyan', text: '检测完成' },
    }
    const config = statusMap[status] || { color: 'default', text: status }
    return (
      <span style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
        <span
          style={{
            width: '6px',
            height: '6px',
            borderRadius: '50%',
            backgroundColor:
              config.color === 'orange'
                ? '#fa8c16'
                : config.color === 'blue'
                  ? '#1890ff'
                  : config.color === 'red'
                    ? '#ff4d4f'
                    : config.color === 'green'
                      ? '#52c41a'
                      : config.color === 'cyan'
                        ? '#13c2c2'
                        : '#d9d9d9',
          }}
        />
        {config.text}
      </span>
    )
  }

  // 重新发起申请
  const handleReapply = (record) => {
    Modal.confirm({
      title: '重新发起申请',
      content: `确定要重新发起申请「${record.cellNameCn || record.cellNameEn}」吗？`,
      onOk() {
        // 注意：这里需要根据实际业务逻辑调用相应的重新发起接口
        // 目前API文档中没有明确的重新发起出库申请接口
        // 可能需要调用 saveDetectionApply 或其他接口
        messageApi.info('重新发起功能待实现')
        return Promise.resolve()
      },
    })
  }

  // 操作按钮渲染
  const renderActions = (text, record) => {
    const actions = []

    // 查看详情按钮 - 待审批/检测中/驳回/检测完成状态可查看
    if ([0, 2, 3, 4].includes(record.status)) {
      actions.push(
        <a
          key="detail"
          onClick={() =>
            push(`/outbound-management/outbound-detail/${record.bid}`)
          }>
          查看详情
        </a>,
      )
    }

    // 重新发起按钮 - 仅驳回状态(status=2)
    if (record.status === 2 && !record.reapplied) {
      actions.push(
        <a
          key="reapply"
          onClick={() => handleReapply(record)}>
          重新发起
        </a>,
      )
    }

    return <Space>{actions}</Space>
  }

  const columns = [
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
      title: '序号',
    },
    {
      title: '状态',
      dataIndex: 'status',
      width: 100,
      render: (status) => renderStatus(status),
      valueType: 'select',
      valueEnum: {
        0: { text: '待审批' },
        1: { text: '通过' },
        2: { text: '驳回' },
        3: { text: '检测中' },
        4: { text: '检测完成' },
      },
      hideInSearch: true,
    },
    {
      title: '出库订单号',
      dataIndex: 'outboundOrderNo',
      width: 150,
    },
    {
      title: '入库订单号',
      dataIndex: 'stockInNo',
      width: 150,
      hideInSearch: true,
    },
    {
      title: '细胞中心编号',
      dataIndex: 'cellCode',
      width: 150,
    },
    {
      title: '细胞名称',
      dataIndex: 'cellName',
      width: 150,
      ellipsis: true,
      render: (_, record) => {
        return record.cellNameCn || record.cellNameEn
      },
      hideInTable: true,
    },
    {
      title: '细胞中文名称',
      dataIndex: 'cellNameCn',
      width: 150,
      ellipsis: true,
      hideInSearch: true,
    },
    {
      title: '细胞英文名称',
      dataIndex: 'cellNameEn',
      width: 150,
      ellipsis: true,
      hideInSearch: true,
    },
    {
      title: '细胞来源',
      dataIndex: 'sourceOrgan',
      width: 120,
    },
    {
      title: '检测项目',
      dataIndex: 'analysisItemText',
      width: 150,
      ellipsis: true,
      hideInSearch: true,
    },
    {
      title: '原始批次号',
      dataIndex: 'originalBatchNo',
      width: 150,
    },
    {
      title: '冻存批次号',
      dataIndex: 'cryoBatchNo',
      width: 150,
    },
    {
      title: '出库管数',
      dataIndex: 'outboundTubeNum',
      width: 100,
      hideInSearch: true,
    },
    {
      title: '出库人',
      dataIndex: 'outboundPerson',
      width: 100,
      hideInSearch: true,
    },
    {
      title: '出库目的',
      dataIndex: 'outboundPurpose',
      width: 120,
      ellipsis: true,
      hideInSearch: true,
    },
    {
      title: '出库标识',
      dataIndex: 'outboundFlag',
      width: 100,
      hideInSearch: true,
    },
    {
      title: '冻存管数',
      dataIndex: 'frozenTubeNum',
      width: 100,
      hideInSearch: true,
    },
    {
      title: '复苏管数',
      dataIndex: 'recoveryTubeNum',
      width: 100,
      hideInSearch: true,
    },
    {
      title: '库存管数',
      dataIndex: 'currentNum',
      width: 100,
      hideInSearch: true,
    },
    {
      title: '细胞代次',
      dataIndex: 'cellGeneration',
      width: 100,
      hideInSearch: true,
    },
    {
      title: '细胞类型',
      dataIndex: 'cellType',
      width: 120,
      hideInSearch: true,
    },
    {
      title: '培养基',
      dataIndex: 'medium',
      width: 120,
      ellipsis: true,
      hideInSearch: true,
    },
    {
      title: '培养条件',
      dataIndex: 'cultureCondition',
      width: 120,
      ellipsis: true,
      hideInSearch: true,
    },
    {
      title: '接收/复苏时间',
      dataIndex: 'recoveryTime',
      width: 250,
      valueType: 'dateTime',
      hideInSearch: true,
    },
    {
      title: '复苏细胞活率',
      dataIndex: 'recoveryViability',
      width: 120,
      hideInSearch: true,
    },
    {
      title: '备注',
      dataIndex: 'remarks',
      width: 150,
      ellipsis: true,
      hideInSearch: true,
    },
    {
      title: '入库管数',
      dataIndex: 'tubeNum',
      width: 100,
      hideInSearch: true,
    },
    {
      title: '入库人',
      dataIndex: 'stockInPerson',
      width: 100,
      hideInSearch: true,
    },
    {
      title: '质检历史',
      dataIndex: 'checkHistory',
      width: 120,
      ellipsis: true,
      hideInSearch: true,
    },
    {
      title: 'STR',
      dataIndex: 'strFlag',
      width: 100,
      hideInSearch: true,
    },
    {
      title: 'STR备注',
      dataIndex: 'strRemark',
      width: 120,
      ellipsis: true,
      hideInSearch: true,
    },
    {
      title: '支原体PCR',
      dataIndex: 'mycoPcr',
      width: 120,
      hideInSearch: true,
    },
    {
      title: '质检人',
      dataIndex: 'qualityInspector',
      width: 100,
      hideInSearch: true,
    },
    {
      title: '质检时间',
      dataIndex: 'checkTime',
      width: 250,
      valueType: 'dateTime',
      hideInSearch: true,
    },
    {
      title: '审核人',
      dataIndex: 'approverName',
      width: 100,
      hideInSearch: true,
    },
    {
      title: '细胞库级别',
      dataIndex: 'storageLibraryLevel',
      width: 120,
      valueType: 'select',
      valueEnum: {
        一级库: { text: '一级库' },
        二级库: { text: '二级库' },
      },
    },
    {
      title: '通过时间',
      dataIndex: 'approveTime',
      width: 150,
      valueType: 'dateTimeRange',
      hideInTable: true,
      search: {
        transform: (value) => {
          return {
            approveTimeStart: value?.[0],
            approveTimeEnd: value?.[1],
          }
        },
      },
    },
    {
      title: '通过时间',
      dataIndex: 'approveTime',
      width: 250,
      valueType: 'dateTime',
      hideInSearch: true,
    },
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      width: 180,
      fixed: 'right',
      render: renderActions,
    },
  ]

  return (
    <>
      {contextHolder}
      <ProTable
        className={tableProStyles.ghost_table_pro}
        columns={columns}
        actionRef={ref}
        ghost={true}
        scroll={{ x: 3000 }}
        request={async (params) => {
          const { current, pageSize, ...searchParams } = params
          const requestData = {
            page: {
              orders: [
                {
                  asc: false,
                  field: 'createdAt',
                },
              ],
              pageNum: current || 1,
              pageSize: pageSize || 10,
            },
            params: {
              ...searchParams,
              applyType: 3, // 出库申请类型
            },
          }
          const res = await selectCellApplyRecordPage(requestData)
          return res
        }}
        form={{
          name: 'outboundManagement',
          // Since transform is configured, the submitted parameters are different from the defined ones, so they need to be transformed here
          syncToUrl: (values, type) => {
            if (type === 'get') {
              return {
                ...values,
                approveTime: [values.approveTimeStart, values.approveTimeEnd],
              }
            }
            return values
          },
        }}
        columnsState={{
          persistenceKey: 'outbound-management-table',
          persistenceType: 'localStorage',
          defaultValue: {
            option: { fixed: 'right', disable: true },
          },
        }}
        rowKey="bid"
        search={{
          labelWidth: 'auto',
        }}
        options={{
          reload: false,
          density: false,
          setting: {
            children: <ColToggleButton />,
          },
        }}
        pagination={{
          size: 'default',
          pageSize: 10,
          showSizeChanger: true,
          showQuickJumper: true,
        }}
      />
    </>
  )
}

export default OutboundManagement
