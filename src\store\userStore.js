import { logout as logoutApi } from '@/api/common'
import { localStorageKey, removeToken, ssoLoginUrl } from '@/utils/config'
import { isEmpty } from '@/utils/is'
import { create } from 'zustand'
import { createJSONStorage, devtools, persist } from 'zustand/middleware'
import { immer } from 'zustand/middleware/immer'

// 初始化state
const initState = {
  // 用户信息
  userInfo: {},
  // 用户角色列表
  userRoles: [],
  // 用户菜单数据（包含菜单和按钮）
  userMenu: [],
  // 用户权限
  userPermissions: [],
}

const store = immer(
  persist(
    (set) => ({
      ...initState,
      actions: {
        setUserInfo: (userInfo) => {
          set({ userInfo })
        },
        // 设置用户角色
        setUserRoles: (userRoles) => {
          let permissions = []

          // 合并用户权限
          if (!isEmpty(userRoles)) {
            userRoles.forEach((item) => {
              permissions = item?.permission
                ? item?.permission?.concat(permissions)
                : permissions
            })
          }
          set({ userRoles, userPermissions: permissions })
        },
        setUserMenu: (userMenu) => {
          set({ userMenu })
        },
        // 清空信息
        clearUserStore() {
          set(initState)
          removeToken()
        },
      },
    }),
    {
      name: localStorageKey.userInfo, // name of the item in the storage (must be unique)
      storage: createJSONStorage(() => localStorage), // (optional) by default, 'localStorage' is used
      partialize: (state) =>
        Object.fromEntries(
          Object.entries(state).filter(([key]) => !['actions'].includes(key)),
        ), // 排除actions方法，防止在持久化时保存该方法
    },
  ),
)

const useUserStore = import.meta.env.DEV
  ? create(devtools(store)) // 开发环境可以使用redux devtools插件调试状态树
  : create(store)

export const useUserInfo = () => useUserStore((state) => state.userInfo)
export const useUserRoles = () => useUserStore((state) => state.userRoles)
export const useUserMenu = () => useUserStore((state) => state.userMenu)
export const useUserPermissions = () =>
  useUserStore((state) => state.userPermissions)

export const useUserActions = () => useUserStore((state) => state.actions)

export const useLogout = () => {
  const { clearUserStore } = useUserActions()

  const logoutAction = () => {
    clearUserStore()
    window.location.href = ssoLoginUrl
  }

  // 退出登录方法
  const logout = async () => {
    try {
      await logoutApi()
    } catch (error) {
      console.error(error)
    } finally {
      logoutAction()
    }
  }

  return logout
}

export default useUserStore
