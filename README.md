# cell-admin-web 细胞平台下的后台管理业务平台

- 鉴定检测
- 库存管理
- ...

## 开发环境

因为后端采用低代码开发。连接VPN进入开发

- node: 20.19.0
- pnpm: 10.6.5

## 配置git区分大小写

`git config core.ignorecase false`

代码提交规范

1. 根据自己名字创建自己分支
2. 把此项目的git.username修改为自己名字
3. 写了代码每天提交一遍

## lint和.prettierrc

- 安装相关插件.vscode\extensions.json
- 按项目配置。代码commit会运行lint检查，不要手动关闭相关检查

## 页面编写

### 定义路由

`src\router\routes.jsx` 路由定义要通过handle属性定义页面title，用于meta和面包屑的生成，具体参照示例

#### 路由分组

对于同属于一个模块的路由要配置到一个路由组里，

例如应急管理的列表和详情路由，都应该在应急管理这个路由前缀下

```javascript
一个包含列表，表单，详情的分组示例
{
  path: 'example-page',
  lazy: async () => ({
    Component: (await import('@/views/example-page/page2')).default,
  }),
  HydrateFallback: () => <PageSpin />,
  handle: () => ({
    title: '示例页面',
    permissions: ['system:example-page'],
  }),
  children: [
    // 有目录的情况添加一个默认路由
    {
      index: true,
      element: (
        <Navigate
          to="list"
          replace
        />
      ),
    },
    {
      path: 'list',
      lazy: async () => ({
        Component: (await import('@/views/example-page/list')).default,
      }),
      HydrateFallback: () => <PageSpin />,
      handle: () => ({
        title: '列表页',
        permissions: ['system:example-page:list'],
      }),
    },
    {
      path: 'form',
      lazy: async () => ({
        Component: (await import('@/views/example-page/form')).default,
      }),
      HydrateFallback: () => <PageSpin />,
      handle: () => ({
        title: '表单页',
        // 不在菜单中的路由要指定激活菜单
        activeMenu: '/example-page/list',
      }),
    },
    {
      path: 'detail/:id',
      lazy: async () => ({
        Component: (await import('@/views/example-page/detail')).default,
      }),
      HydrateFallback: () => <PageSpin />,
      handle: () => ({
        title: '详情页',
        activeMenu: '/example-page/list',
      }),
    },
  ],
},
```

### 表格等组件

使用`ProComponents`组件库 ，文档地址：`https://pro-components.antdigital.dev/`

按目前设计图有两种使用方式，

- 默认的是搜索区和表体分开
- 搜索区和表体结合，使用ghost模式，传入预定义的`className`，具体查看示例代码

```javascript
import tableProStyles from '@/assets/styles/ant-pro.module.scss'

<ProTable
  className={tableProStyles.ghost_table_pro}
  columns={columns}
  ghost={true}
  ...props
/>
```

![](docs\images\两种table设计.png)

![](docs\images\表格文档.jpg)

#### 数据转换

因为`ProTable`和`ProList`组件要求特定的数据格式，所以需要对后端表格数据进行转换

组件所需要的数据

```javascript
{
  // 承载表格数据
  data: [],
  // 不然 table 会停止解析数据，即使有数据
  success: true,
  total: 0,
}
```

后端的数据结构一般为

```javascript
{
  "page": 1,
  "pageSize": 50,
  "total": 30,
  "list": [],
  "hasNext": false
}
```

- 常规情况直接传入`transformType: 'proTable'`在全局转换器中自动进行转换
  ```javascript
  export function testTable(data) {
    return get('/test-pro-table-list', data, {
      // 转换类型
      transformType: 'proTable',
    })
  }
  ```
- 其他特殊情况自己编写transform函数，会替换全局转换器，按要求的数据格式返回数据即可
  ```javascript
  export function testTable(data) {
    return get('/test-pro-table-list', data, {
      transform: (res) => {
        return {
          data: res.data.data.list,
          total: res.data.data.total,
          success: true,
        }
      },
    })
  }
  ```

#### 字段管理持久化

添加`columnsState`和`options.setting` ，`persistenceKey`需要唯一table名进行持久化列字段配置

```javascript
<ProTable
  columns={columns}
  columnsState={{
    persistenceKey: 'pro-table-singe-demos',
    persistenceType: 'localStorage',
    defaultValue: {
      // 某一项固定
      option: { fixed: 'right', disable: true },
    },
  }}
  rowKey="id"
  options={{
    reload: false,
    density: false,
    setting: {
      children: <ColToggleButton />,
    },
  }}
/>
```

#### 表格分页

按设计图使用默认大小，可设置数量和快速跳转

```javascript
<ProTable
  columns={columns}
  pagination={{
    size: 'default',
    pageSize: 20,
    showSizeChanger: true,
    showQuickJumper: true,
  }}
/>
```

### 面包屑

使用全局封装的自动面包屑组件

## 接口编写与调用

### 接口定义

`src\api`文件夹下引入封装的`alova` 各类型请求建立各模块接口

```javascript
import { get } from '@/utils/request/alova'

/**
 * axios 已经默认配置了baseUrl
 * @param {*} data
 * @returns
 */
export function testApiSuccess(data) {
  return get(`/getUsers`, data)
}
```

### 接口mock

按接口分组编写mock接口

![image-20250620112921716](docs\images\按分组编写mock接口.jpg)

#### 返回值定义

![image-20250620112921716](docs\images\mock返回值定义.png)

**把需要返回的结构定义在data里，会经过转换程序就行处理**

```javascript
return {
  code: 0,
  msg: 'ok',
  // 把需要返回的结构定义在data里，会经过转换程序就行处理
  data: {
    total: 100,
    data: [],
  },
}
```

### 接口调用

**使用`alova.js`调用接口**

- 参照`https://alova.js.org/zh-CN/`各场景调用接口

* 自动维护状态
* 拥有缓存和重复调用逻辑处理

- 普通调用

```javascript
const { data, loading, error } = useRequest(todoListGetter, {
  initialData: [],
})
```

- 分页调用

```javascript
// 表格数据
const {
  // 加载状态
  loading,

  // 列表数据
  data: tableData,

  // 当前页码，改变此页码将自动触发请求
  page,

  // 每页数据条数
  pageSize,

  // 总数据量
  total,

  reload,
} = usePagination(
  // Method实例获取函数，它将接收page和pageSize，并返回一个Method实例
  (page, pageSize) =>
    getProjectRepoList({
      page: {
        pageNum: page,
        pageSize,
      },
      params: searchForm.value,
    }),
  {
    // 请求前的初始数据（接口返回的数据格式）
    initialData: {
      total: 0,
      list: [],
    },
    // 分页格式不同则需要制定
    total: (response) => response.total,
    data: (response) => response.list,
    initialPage: 1, // 初始页码，默认为1
    initialPageSize: 10, // 初始每页数据条数，默认为10
  },
)
```

- useAntdTable （useAntdTable 基于 useRequest 实现，封装了常用的 Ant Design Form 与 Ant Design Table 联动逻辑，并且同时支持 antd v3 和 v4。）

https://ahooks.js.org/zh-CN/hooks/use-antd-table

## hooks 库

- ahooks（https://ahooks.js.org）
