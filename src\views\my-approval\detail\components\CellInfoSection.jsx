import { CaretRightOutlined } from '@ant-design/icons'
import { Collapse, Descriptions } from 'antd'

const { Panel } = Collapse

function CellInfoSection({ data }) {
  const items = [
    {
      key: 'cellCenterNumber',
      label: '细胞中心编号',
      children: data?.cellCenterNumber || '-',
    },
    {
      key: 'cellChineseName',
      label: '细胞中文名称',
      children: data?.cellChineseName || '-',
    },
    {
      key: 'cellEnglishName',
      label: '细胞英文名称',
      children: data?.cellEnglishName || '-',
    },
    {
      key: 'cellSpecies',
      label: '细胞种属',
      children: data?.cellSpecies || '-',
    },
    {
      key: 'cellSource',
      label: '细胞来源',
      children: data?.cellSource || '-',
    },
    {
      key: 'originalBatchNumber',
      label: '原始批次号',
      children: data?.originalBatchNumber || '-',
    },
    {
      key: 'frozenBatchNumber',
      label: '冻存批次号',
      children: data?.frozenBatchNumber || '-',
    },
    {
      key: 'cellType',
      label: '细胞类型',
      children: data?.cellType || '-',
    },
    {
      key: 'frozenTubeCount',
      label: '冻存管数',
      children: data?.frozenTubeCount || '-',
    },
    {
      key: 'resuscitationTubeCount',
      label: '复苏管数',
      children: data?.resuscitationTubeCount || '-',
    },
    {
      key: 'stockTubeCount',
      label: '库存管数',
      children: data?.stockTubeCount || '-',
    },
    {
      key: 'cellGeneration',
      label: '细胞代次',
      children: data?.cellGeneration || '-',
    },
    {
      key: 'cultureMedium',
      label: '培养基',
      children: data?.cultureMedium || '-',
    },
    {
      key: 'cultureConditions',
      label: '培养条件',
      children: data?.cultureConditions || '-',
    },
    {
      key: 'receiveResuscitationTime',
      label: '接收/复苏时间',
      children: data?.receiveResuscitationTime || '-',
    },
    {
      key: 'resuscitationCellViability',
      label: '复苏细胞活率',
      children: data?.resuscitationCellViability || '-',
    },
    {
      key: 'cellLibraryLevel',
      label: '细胞库级别',
      children: data?.cellLibraryLevel || '-',
    },
    {
      key: 'remark',
      label: '备注',
      children: data?.remark || '-',
    },
  ]

  return (
    <Collapse
      defaultActiveKey={['cellInfo']}
      expandIcon={({ isActive }) => (
        <CaretRightOutlined rotate={isActive ? 90 : 0} />
      )}
      expandIconPosition="end">
      <Panel
        header={
          <span style={{ fontSize: '16px', fontWeight: 'bold' }}>细胞信息</span>
        }
        key="cellInfo">
        <Descriptions
          column={3}
          size="small"
          items={items}
        />
      </Panel>
    </Collapse>
  )
}

export default CellInfoSection
