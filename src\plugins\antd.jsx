import { App as AntdApp, ConfigProvider } from 'antd'
import zhCN from 'antd/locale/zh_CN'

import styleVariables from '@/assets/styles/variables.module.scss'

// antd 封装
function AntdUi(props) {
  const { primary } = styleVariables

  return (
    <ConfigProvider
      locale={zhCN}
      theme={{
        cssVar: true,
        hashed: false,
        token: {
          colorPrimary: primary,
        },
      }}>
      <AntdApp>{props.children}</AntdApp>
    </ConfigProvider>
  )
}

export default AntdUi
