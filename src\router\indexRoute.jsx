import { useMemo } from 'react'
import { Navigate } from 'react-router'

import TreeUtils from '@/utils/tree'

import { useLogout, useUserMenu } from '@/store/userStore'
import { Button, Result } from 'antd'

// 嵌套路由的默认路由
function IndexRoute() {
  const menuTree = useUserMenu()
  const logout = useLogout()

  // 获取到此用户菜单的第一个叶子节点路由 作为默认跳转路由
  const indexPath = useMemo(() => {
    return (
      TreeUtils.findNode(
        menuTree || [],
        (node) => node.path !== '' && !node?.children?.length,
      )?.path || ''
    )
  }, [menuTree])

  if (!indexPath)
    return (
      <Result
        className="tw-pt-48"
        status="error"
        title="你没有系统的任何菜单权限！"
        extra={
          <Button
            type="primary"
            key="console"
            onClick={logout}>
            重新登录或刷新界面
          </Button>
        }
      />
    )

  return (
    <Navigate
      to={indexPath}
      replace={true}
    />
  )
}

export default IndexRoute
