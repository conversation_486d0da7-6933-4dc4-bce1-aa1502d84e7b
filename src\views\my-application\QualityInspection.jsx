import { abandoningStorage, applyRecheck } from '@/api/myApplication'
import { selectCellApplyRecordPage } from '@/api/myApproval'
import tableProStyles from '@/assets/styles/ant-pro.module.scss'
import ColToggleButton from '@/components/Buttons/ColToggleButton'
import { useRouter } from '@/hooks/router'
import { PlusOutlined } from '@ant-design/icons'
import { ProTable } from '@ant-design/pro-components'
import { Button, Checkbox, Modal, Space, message } from 'antd'
import { useRef, useState } from 'react'

function QualityInspection() {
  const { push } = useRouter()
  const [messageApi, contextHolder] = message.useMessage()
  const ref = useRef(null)
  const [abandonModalVisible, setAbandonModalVisible] = useState(false)
  const [retestModalVisible, setRetestModalVisible] = useState(false)
  const [currentRecord, setCurrentRecord] = useState(null)
  const [selectedRetestItems, setSelectedRetestItems] = useState([])

  // 状态标签渲染 - 根据接口文档更新状态映射
  const renderStatus = (status) => {
    const statusMap = {
      0: { color: 'orange', text: '待审批' },
      1: { color: 'green', text: '审批通过' },
      2: { color: 'red', text: '审批驳回' },
      3: { color: 'blue', text: '检测中' },
      4: { color: 'cyan', text: '检测完成' },
      5: { color: 'gray', text: '已完成' },
    }
    const config = statusMap[status] || { color: 'default', text: status }
    return (
      <span style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
        <span
          style={{
            width: '6px',
            height: '6px',
            borderRadius: '50%',
            backgroundColor:
              config.color === 'orange'
                ? '#fa8c16'
                : config.color === 'blue'
                  ? '#1890ff'
                  : config.color === 'red'
                    ? '#ff4d4f'
                    : config.color === 'green'
                      ? '#52c41a'
                      : config.color === 'cyan'
                        ? '#13c2c2'
                        : '#d9d9d9',
          }}
        />
        {config.text}
      </span>
    )
  }

  // 操作按钮渲染 - 根据接口文档更新状态判断
  const renderActions = (text, record) => {
    const actions = []

    // 查看详情按钮 - 所有状态都可查看
    if ([0, 1, 2, 3, 4].includes(record.status)) {
      actions.push(
        <a
          key="detail"
          onClick={() => push(`/my-application/quality-detail/${record.bid}`)}>
          查看详情
        </a>,
      )
    }

    // 重新发起按钮 - 仅审批驳回状态(status=2)
    if (record.status === 2 && !record.reapplied) {
      actions.push(
        <a
          key="reapply"
          onClick={() =>
            push(`/my-application/quality-apply?applyBid=${record.bid}`)
          }>
          重新发起
        </a>,
      )
    }

    // 检测完成状态的操作按钮(status=4)
    if (record.status === 4) {
      actions.push(
        <a
          key="storage"
          onClick={() => push(`/inventory/add-storage?cellId=${record.bid}`)}>
          入库
        </a>,
        <a
          key="abandon"
          onClick={() => handleAbandonStorage(record)}>
          放弃入库
        </a>,
        <a
          key="retest"
          onClick={() => handleApplyRetest(record)}>
          申请复检
        </a>,
      )
    }

    return <Space>{actions}</Space>
  }

  // 放弃入库处理
  const handleAbandonStorage = (record) => {
    setCurrentRecord(record)
    setAbandonModalVisible(true)
  }

  // 确认放弃入库 - 使用实际的记录ID
  const confirmAbandonStorage = async () => {
    try {
      await abandoningStorage(currentRecord.bid)
      messageApi.success('操作成功')
      setAbandonModalVisible(false)
      // 刷新表格
      ref.current?.reload()
    } catch (error) {
      console.log(error)
    }
  }

  // 申请复检处理
  const handleApplyRetest = (record) => {
    setCurrentRecord(record)
    setSelectedRetestItems([])
    setRetestModalVisible(true)
  }

  // 确认申请复检 - 使用实际的记录ID
  const confirmApplyRetest = async () => {
    if (selectedRetestItems.length === 0) {
      messageApi.warning('请至少选择一个复检项目')
      return
    }
    try {
      await applyRecheck(currentRecord.bid, selectedRetestItems.join(','))
      messageApi.success('申请复检成功')
      setRetestModalVisible(false)
      ref.current?.reload()
    } catch (error) {
      console.log(error)
    }
  }

  // 复检项目选项
  const retestOptions = [
    { label: 'STR', value: 1 },
    { label: '同工酶', value: 2 },
    { label: 'PCR种属鉴定', value: 3 },
    { label: 'DNA Barcoding', value: 4 },
    { label: '支原体培养法', value: 5 },
    { label: '支原体PCR检测法', value: 6 },
    { label: '支原体DNA染色法', value: 7 },
    { label: '细菌真菌接种法', value: 8 },
  ]

  const columns = [
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      width: 240,
      fixed: 'left',
      render: renderActions,
    },
    {
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 48,
      title: '序号',
    },
    {
      title: '状态',
      dataIndex: 'status',
      width: 100,
      render: (status) => renderStatus(status),
      valueType: 'select',
      valueEnum: {
        0: { text: '待审批' },
        1: { text: '审批通过' },
        2: { text: '审批驳回' },
        3: { text: '检测中' },
        4: { text: '检测完成' },
        5: { text: '已完成' },
      },
      hideInSearch: true,
    },
    {
      title: '入库订单号',
      dataIndex: 'stockInNo',
      width: 150,
    },
    {
      title: '细胞中心编号',
      dataIndex: 'cellCode',
      width: 150,
    },
    {
      title: '细胞名称',
      dataIndex: 'cellName',
      width: 150,
      ellipsis: true,
      render: (_, record) => {
        return record.cellNameCn || record.cellNameEn
      },
      hideInTable: true,
    },
    {
      title: '细胞中文名称',
      dataIndex: 'cellNameCn',
      width: 150,
      ellipsis: true,
      hideInSearch: true,
    },
    {
      title: '细胞英文名称',
      dataIndex: 'cellNameEn',
      width: 150,
      ellipsis: true,
      hideInSearch: true,
    },
    {
      title: '细胞来源',
      dataIndex: 'sourceOrgan',
      width: 120,
    },
    {
      title: '细胞库级别',
      dataIndex: 'storageLibraryLevel',
      width: 120,
      valueType: 'select',
      valueEnum: {
        一级库: { text: '一级库' },
        二级库: { text: '二级库' },
        三级库: { text: '三级库' },
      },
    },
    {
      title: '检测项目',
      dataIndex: 'analysisItemText',
      width: 150,
      ellipsis: true,
      hideInSearch: true,
    },
    {
      title: '原始批次号',
      dataIndex: 'originalBatchNo',
      width: 150,
    },
    {
      title: '冻存批次号',
      dataIndex: 'cryoBatchNo',
      width: 150,
    },
    {
      title: '库存管数',
      dataIndex: 'currentNum',
      width: 100,
      hideInSearch: true,
    },
    {
      title: '细胞代次',
      dataIndex: 'cellGeneration',
      width: 100,
      hideInSearch: true,
    },
    {
      title: '细胞类型',
      dataIndex: 'cellType',
      width: 120,
      hideInSearch: true,
    },
    {
      title: '培养基',
      dataIndex: 'medium',
      width: 120,
      ellipsis: true,
      hideInSearch: true,
    },
    {
      title: '培养条件',
      dataIndex: 'cultureCondition',
      width: 120,
      ellipsis: true,
      hideInSearch: true,
    },
    {
      title: '接收/复苏时间',
      dataIndex: 'recoveryTime',
      width: 150,
      valueType: 'dateTime',
      hideInSearch: true,
    },
    {
      title: '复苏细胞活率',
      dataIndex: 'recoveryViability',
      width: 120,
      hideInSearch: true,
    },
    {
      title: '备注',
      dataIndex: 'remarks',
      width: 150,
      ellipsis: true,
      hideInSearch: true,
    },
    {
      title: '入库管数',
      dataIndex: 'tubeNum',
      width: 100,
      hideInSearch: true,
    },
    {
      title: '质检历史',
      dataIndex: 'checkHistory',
      width: 120,
      ellipsis: true,
      hideInSearch: true,
    },
    {
      title: 'STR',
      dataIndex: 'strFlag',
      width: 100,
      hideInSearch: true,
    },
    {
      title: 'STR备注',
      dataIndex: 'strRemark',
      width: 120,
      ellipsis: true,
      hideInSearch: true,
    },
    {
      title: '支原体PCR',
      dataIndex: 'mycoPcr',
      width: 120,
      hideInSearch: true,
    },
    {
      title: '支原体培养',
      dataIndex: 'mycoCulture',
      width: 120,
      hideInSearch: true,
    },
    {
      title: '支原体DNA',
      dataIndex: 'mycoDna',
      width: 120,
      hideInSearch: true,
    },
    {
      title: '种属鉴定PCR',
      dataIndex: 'speciesPcr',
      width: 150,
      hideInSearch: true,
    },
    {
      title: '细菌真菌25℃',
      dataIndex: 'fungusLowTemp',
      width: 120,
      hideInSearch: true,
    },
    {
      title: '细菌真菌35℃',
      dataIndex: 'fungusHighTemp',
      width: 120,
      hideInSearch: true,
    },
    {
      title: 'DNA Barcoding',
      dataIndex: 'dnaBarcoding',
      width: 150,
      hideInSearch: true,
    },
    {
      title: '质检时间',
      dataIndex: 'checkTime',
      width: 250,
      valueType: 'dateTime',
      hideInSearch: true,
    },
    {
      title: '审核人',
      dataIndex: 'approverName',
      width: 100,
      hideInSearch: true,
    },
    {
      title: '通过时间',
      dataIndex: 'approveTime',
      width: 150,
      valueType: 'dateTimeRange',
      hideInTable: true,
      search: {
        transform: (value) => {
          return {
            approveTimeStart: value?.[0],
            approveTimeEnd: value?.[1],
          }
        },
      },
    },
    {
      title: '通过时间',
      dataIndex: 'approveTime',
      width: 250,
      valueType: 'dateTime',
      hideInSearch: true,
    },
  ]

  return (
    <>
      {contextHolder}
      <ProTable
        className={tableProStyles.ghost_table_pro}
        columns={columns}
        actionRef={ref}
        ghost={true}
        scroll={{ x: 2500 }}
        request={async (params) => {
          const { current, pageSize, ...searchParams } = params
          const requestData = {
            page: {
              orders: [
                {
                  asc: false,
                  field: 'createdAt',
                },
              ],
              pageNum: current || 1,
              pageSize: pageSize || 10,
            },
            params: {
              ...searchParams,
              applyType: 1, // 质检申请
            },
          }
          const res = await selectCellApplyRecordPage(requestData)
          return res
        }}
        form={{
          name: 'qualityInspection',
          // Since transform is configured, the submitted parameters are different from the defined ones, so they need to be transformed here
          syncToUrl: (values, type) => {
            if (type === 'get') {
              return {
                ...values,
                approveTime: [values.approveTimeStart, values.approveTimeEnd],
              }
            }
            return values
          },
        }}
        columnsState={{
          persistenceKey: 'quality-inspection-table',
          persistenceType: 'localStorage',
          defaultValue: {
            option: { fixed: 'right', disable: true },
          },
        }}
        rowKey="bid"
        search={{
          labelWidth: 'auto',
        }}
        options={{
          reload: false,
          density: false,
          setting: {
            children: <ColToggleButton />,
          },
        }}
        pagination={{
          size: 'default',
          pageSize: 10,
          showSizeChanger: true,
          showQuickJumper: true,
        }}
        headerTitle={
          <Button
            key="apply"
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => push('/my-application/quality-apply')}>
            质检申请
          </Button>
        }
      />

      {/* 放弃入库弹窗 */}
      <Modal
        title="放弃入库"
        open={abandonModalVisible}
        onOk={confirmAbandonStorage}
        onCancel={() => setAbandonModalVisible(false)}
        okText="确认"
        cancelText="取消">
        <p>确认放弃将当前细胞入库？</p>
      </Modal>

      {/* 申请复检弹窗 */}
      <Modal
        title="选择复检项目"
        open={retestModalVisible}
        onOk={confirmApplyRetest}
        onCancel={() => setRetestModalVisible(false)}
        okText="确认"
        cancelText="取消"
        width={500}>
        <div style={{ marginBottom: 16 }}>
          <p>请选择需要复检的项目：</p>
        </div>
        <Checkbox.Group
          options={retestOptions}
          value={selectedRetestItems}
          onChange={setSelectedRetestItems}
        />
      </Modal>
    </>
  )
}

export default QualityInspection
