import { useMemo } from 'react'
import { useNavigate } from 'react-router'

/**
 * 路由导航钩子函数
 * @returns {{
 *   back: () => void,
 *   forward: () => void,
 *   reload: () => void,
 *   push: (href: string) => void,
 *   replace: (href: string) => void
 * }} 路由导航对象
 */
export function useRouter() {
  const navigate = useNavigate()

  const router = useMemo(
    () => ({
      back: () => navigate(-1),
      forward: () => navigate(1),
      reload: () => window.location.reload(),
      push: (href) => navigate(href),
      replace: (href) => navigate(href, { replace: true }),
    }),
    [navigate],
  )

  return router
}
