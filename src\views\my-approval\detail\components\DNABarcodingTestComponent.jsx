import { Descriptions } from 'antd'

// DNA Barcoding检测组件
function DNABarcodingTestComponent({ data }) {
  return (
    <Descriptions
      column={3}
      size="small"
      items={[
        {
          key: 'barcodeSequence',
          label: '条形码序列',
          children: data?.barcodeSequence || '-',
        },
        {
          key: 'similarity',
          label: '相似度',
          children: data?.similarity || '-',
        },
        {
          key: 'testMethod',
          label: '检测方法',
          children: data?.testMethod || '-',
        },
      ]}
    />
  )
}

export default DNABarcodingTestComponent
