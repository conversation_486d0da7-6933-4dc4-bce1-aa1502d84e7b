import { Descriptions } from 'antd'

// 支原体PCR检测法组件
function MycoplasmaPCRTestComponent({ data }) {
  return (
    <Descriptions
      column={3}
      size="small"
      items={[
        {
          key: 'pcrResult',
          label: 'PCR结果',
          children: data?.pcrResult || '-',
        },
        {
          key: 'targetGene',
          label: '目标基因',
          children: data?.targetGene || '-',
        },
        {
          key: 'testMethod',
          label: '检测方法',
          children: data?.testMethod || '-',
        },
      ]}
    />
  )
}

export default MycoplasmaPCRTestComponent
