import { selectCellApplyRecordPage } from '@/api/myApproval'
import tableProStyles from '@/assets/styles/ant-pro.module.scss'
import ColToggleButton from '@/components/Buttons/ColToggleButton'
import { useRouter } from '@/hooks/router'
import { ProTable } from '@ant-design/pro-components'
import { Button, message, Modal, Progress, Space } from 'antd'
import { useRef, useState } from 'react'
import { getBaseColumns } from './columns'

function AmplificationApproval() {
  const [exportModalVisible, setExportModalVisible] = useState(false)
  const [exportProgress, setExportProgress] = useState(0)
  const [messageApi, contextHolder] = message.useMessage()
  const { push } = useRouter()
  const ref = useRef(null)

  // 操作按钮渲染
  const renderActions = (text, record) => {
    const actions = []

    // 查看详情按钮
    actions.push(
      <a
        key="detail"
        onClick={() => push(`/my-approval/detail/${record.bid}`)}>
        查看详情
      </a>,
    )

    // 已通过状态显示出库按钮
    if (record.status === 1) {
      actions.push(
        <a
          key="outbound"
          onClick={() =>
            push(`/my-approval/amplification-outbound/${record.bid}`)
          }>
          出库
        </a>,
      )
    }

    return <Space>{actions}</Space>
  }

  // 导出清单处理
  const handleExport = () => {
    setExportProgress(0)
    setExportModalVisible(true)

    // 模拟导出进度
    const timer = setInterval(() => {
      setExportProgress((prev) => {
        if (prev >= 100) {
          clearInterval(timer)
          setTimeout(() => {
            setExportModalVisible(false)
            messageApi.success('导出成功')
          }, 500)
          return 100
        }
        return prev + 10
      })
    }, 200)
  }

  // 扩增审批表格列配置
  const amplificationColumns = [
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      width: 180,
      fixed: 'right',
      render: renderActions,
    },
    ...getBaseColumns(),
  ]

  return (
    <>
      {contextHolder}
      <ProTable
        className={tableProStyles.ghost_table_pro}
        columns={amplificationColumns}
        actionRef={ref}
        ghost={true}
        scroll={{ x: 2500 }}
        request={async (params) => {
          const { current, pageSize, ...searchParams } = params
          const requestData = {
            page: {
              orders: [
                {
                  asc: false,
                  field: 'createdAt',
                },
              ],
              pageNum: current || 1,
              pageSize: pageSize || 10,
            },
            params: {
              ...searchParams,
              applyType: 2, // 扩增申请类型
              status: 0, // 只显示待审批的
            },
          }
          const res = await selectCellApplyRecordPage(requestData)
          return res
        }}
        form={{
          name: 'amplificationApproval',
          syncToUrl: (values, type) => {
            if (type === 'get') {
              return {
                ...values,
                createdAt: [values.createdAtStart, values.createdAtEnd],
              }
            }
            return values
          },
        }}
        columnsState={{
          persistenceKey: 'amplification-approval-table',
          persistenceType: 'localStorage',
          defaultValue: {
            option: { fixed: 'right', disable: true },
          },
        }}
        rowKey="bid"
        search={{
          labelWidth: 'auto',
        }}
        options={{
          reload: false,
          density: false,
          setting: {
            children: <ColToggleButton />,
          },
        }}
        pagination={{
          size: 'default',
          pageSize: 10,
          showSizeChanger: true,
          showQuickJumper: true,
        }}
        headerTitle={
          <Button
            type="primary"
            onClick={handleExport}>
            导出清单
          </Button>
        }
      />

      {/* 导出进度弹窗 */}
      <Modal
        title="导出清单"
        open={exportModalVisible}
        footer={null}
        closable={false}
        width={400}>
        <div style={{ textAlign: 'center', padding: '20px 0' }}>
          <Progress
            type="circle"
            percent={exportProgress}
            status={exportProgress === 100 ? 'success' : 'active'}
          />
          <div style={{ marginTop: '16px', color: '#666' }}>
            {exportProgress === 100 ? '导出完成' : '正在导出中...'}
          </div>
        </div>
      </Modal>
    </>
  )
}

export default AmplificationApproval
