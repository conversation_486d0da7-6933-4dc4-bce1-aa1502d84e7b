import { Descriptions } from 'antd'

// 细菌真菌接种法检测组件
function BacteriaFungiInoculationTestComponent({ data }) {
  return (
    <Descriptions
      column={3}
      size="small"
      items={[
        {
          key: 'bacteriaResult',
          label: '细菌检测结果',
          children: data?.bacteriaResult || '-',
        },
        {
          key: 'fungiResult',
          label: '真菌检测结果',
          children: data?.fungiResult || '-',
        },
        {
          key: 'inoculationMethod',
          label: '接种方法',
          children: data?.inoculationMethod || '-',
        },
      ]}
    />
  )
}

export default BacteriaFungiInoculationTestComponent
