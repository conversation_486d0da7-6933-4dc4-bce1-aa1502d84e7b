import {
  approveApply,
  getCellApplyInfoDetail,
  rejectApply,
} from '@/api/myApproval'
import Breadcrumb from '@/components/Breadcrumb'
import Content from '@/layout/components/Content'
import { Button, Card, Input, message, Modal, Space, Spin, Switch } from 'antd'
import { useCallback, useEffect, useState } from 'react'
import { useNavigate, useParams } from 'react-router'
import CellInfoSection from './components/CellInfoSection'
import InboundInfoSection from './components/InboundInfoSection'
import OutboundInfoSection from './components/OutboundInfoSection'
import QualityInfoSection from './components/QualityInfoSection'
import { getMockDetailData } from './mockData'

function ApprovalDetail() {
  const { id } = useParams()
  const navigate = useNavigate()
  const [loading, setLoading] = useState(true)
  const [detailData, setDetailData] = useState(null)
  const [useMockData, setUseMockData] = useState(false)
  const [actionLoading, setActionLoading] = useState(false)

  // 获取详情数据
  const fetchDetailData = useCallback(async () => {
    try {
      setLoading(true)
      let res
      if (useMockData) {
        res = await getMockDetailData()
      } else {
        res = await getCellApplyInfoDetail(id)
      }
      setDetailData(res.data)
    } catch (error) {
      console.error('获取详情数据失败:', error)
    } finally {
      setLoading(false)
    }
  }, [id, useMockData])

  useEffect(() => {
    if (id || useMockData) {
      fetchDetailData()
    }
  }, [id, useMockData, fetchDetailData])

  // 处理审批操作
  const handleApproval = useCallback(
    async (action, reason = '') => {
      try {
        setActionLoading(true)

        if (action === 'approve') {
          await approveApply(id)
        } else {
          await rejectApply(id, reason)
        }

        message.success(action === 'approve' ? '审批通过成功' : '驳回成功')

        // 返回列表页
        navigate('/my-approval')
      } catch (error) {
        console.error('审批操作失败:', error)
        message.error('操作失败，请重试')
      } finally {
        setActionLoading(false)
      }
    },
    [id, navigate],
  )

  // 通过审批
  const handleApprove = useCallback(() => {
    Modal.confirm({
      title: '确认通过',
      content: '确定要通过此申请吗？',
      onOk: () => handleApproval('approve'),
    })
  }, [handleApproval])

  // 驳回审批
  const handleReject = useCallback(() => {
    let rejectReason = ''

    Modal.confirm({
      title: '驳回申请',
      content: (
        <div className="mt-4">
          <p className="mb-2">请填写驳回原因：</p>
          <Input.TextArea
            placeholder="请输入驳回原因"
            rows={4}
            onChange={(e) => {
              rejectReason = e.target.value
            }}
          />
        </div>
      ),
      onOk: () => {
        if (!rejectReason.trim()) {
          message.error('请填写驳回原因')
          return Promise.reject()
        }
        return handleApproval('reject', rejectReason)
      },
      okText: '确认驳回',
      cancelText: '取消',
    })
  }, [handleApproval])

  if (loading) {
    return (
      <Content>
        <div className="p-12 text-center">
          <Spin size="large" />
        </div>
      </Content>
    )
  }

  return (
    <div className="flex h-full flex-col">
      {/* 固定的面包屑区域 */}
      <div className="flex-shrink-0 border-b border-gray-200 bg-white p-[18px]">
        <Breadcrumb
          items={[
            { title: '我的审批', path: '/my-approval' },
            { title: '审批详情' },
          ]}>
          <Space>
            <Button
              type="primary"
              danger
              loading={actionLoading}
              onClick={handleReject}>
              驳回
            </Button>
            <Button
              type="primary"
              loading={actionLoading}
              onClick={handleApprove}>
              通过
            </Button>
          </Space>
        </Breadcrumb>

        {/* 开发模式：Mock数据开关 */}
        {/* eslint-disable-next-line no-undef */}
        {process.env.NODE_ENV === 'development' && (
          <div className="mt-4 border-t border-gray-200 pt-4">
            <Space>
              <span>开发模式:</span>
              <Switch
                checked={useMockData}
                onChange={setUseMockData}
                checkedChildren="Mock数据"
                unCheckedChildren="真实数据"
              />
            </Space>
          </div>
        )}
      </div>

      {/* 可滚动的内容区域 */}
      <div className="flex-1 overflow-auto bg-white p-6">
        <Card className="!mb-4">
          <CellInfoSection data={detailData?.cellInfo} />
        </Card>

        <Card className="!mb-4">
          <InboundInfoSection data={detailData?.inboundInfo} />
        </Card>

        <Card className="!mb-4">
          <OutboundInfoSection data={detailData?.outboundInfo} />
        </Card>

        <Card>
          <QualityInfoSection data={detailData?.qualityInfo} />
        </Card>
      </div>
    </div>
  )
}

export default ApprovalDetail
