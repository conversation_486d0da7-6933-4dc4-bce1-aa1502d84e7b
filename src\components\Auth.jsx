import {
  useHasPermi,
  useHasPermiAnd,
  useHasPermiOr,
  useHasRole,
  useHasRoleAnd,
  useHasRoleOr,
} from '@/hooks/useAuth'

/**
 * 单个权限控制组件
 * @param {String} permission - 需要验证的权限
 * @param {ReactNode} children - 子组件
 * @param {ReactNode} fallback - 无权限时显示的内容，默认为null
 */
export function PermissionGuard({ permission, children, fallback = null }) {
  const hasPermission = useHasPermi(permission)

  return hasPermission ? children : fallback
}

/**
 * 多个权限控制组件（或关系）- 只需包含其中一个权限
 * @param {Array} permissions - 需要验证的权限列表
 * @param {ReactNode} children - 子组件
 * @param {ReactNode} fallback - 无权限时显示的内容，默认为null
 */
export function PermissionGuardOr({ permissions, children, fallback = null }) {
  const hasPermission = useHasPermiOr(permissions)

  return hasPermission ? children : fallback
}

/**
 * 多个权限控制组件（且关系）- 必须拥有全部权限
 * @param {Array} permissions - 需要验证的权限列表
 * @param {ReactNode} children - 子组件
 * @param {ReactNode} fallback - 无权限时显示的内容，默认为null
 */
export function PermissionGuardAnd({ permissions, children, fallback = null }) {
  const hasPermission = useHasPermiAnd(permissions)

  return hasPermission ? children : fallback
}

/**
 * 单个角色控制组件
 * @param {String} role - 需要验证的角色
 * @param {ReactNode} children - 子组件
 * @param {ReactNode} fallback - 无权限时显示的内容，默认为null
 */
export function RoleGuard({ role, children, fallback = null }) {
  const hasRole = useHasRole(role)

  return hasRole ? children : fallback
}

/**
 * 多个角色控制组件（或关系）- 只需包含其中一个角色
 * @param {Array} roles - 需要验证的角色列表
 * @param {ReactNode} children - 子组件
 * @param {ReactNode} fallback - 无权限时显示的内容，默认为null
 */
export function RoleGuardOr({ roles, children, fallback = null }) {
  const hasRole = useHasRoleOr(roles)

  return hasRole ? children : fallback
}

/**
 * 多个角色控制组件（且关系）- 必须拥有全部角色
 * @param {Array} roles - 需要验证的角色列表
 * @param {ReactNode} children - 子组件
 * @param {ReactNode} fallback - 无权限时显示的内容，默认为null
 */
export function RoleGuardAnd({ roles, children, fallback = null }) {
  const hasRole = useHasRoleAnd(roles)

  return hasRole ? children : fallback
}

/**
 * 综合权限控制组件 - 支持权限和角色的复合验证
 * @param {String} permission - 单个权限
 * @param {Array} permissions - 权限列表
 * @param {String} permissionLogic - 权限逻辑关系：'and' | 'or'，默认'and'
 * @param {String} role - 单个角色
 * @param {Array} roles - 角色列表
 * @param {String} roleLogic - 角色逻辑关系：'and' | 'or'，默认'and'
 * @param {String} combineLogic - 权限和角色之间的逻辑关系：'and' | 'or'，默认'and'
 * @param {ReactNode} children - 子组件
 * @param {ReactNode} fallback - 无权限时显示的内容，默认为null
 */
export function AuthGuard({
  permission,
  permissions = [],
  permissionLogic = 'and',
  role,
  roles = [],
  roleLogic = 'and',
  combineLogic = 'and',
  children,
  fallback = null,
}) {
  // 始终调用所有Hook，避免条件调用
  const hasSinglePermission = useHasPermi(permission || '')
  const hasPermissionsOr = useHasPermiOr(permissions)
  const hasPermissionsAnd = useHasPermiAnd(permissions)
  const hasSingleRole = useHasRole(role || '')
  const hasRolesOr = useHasRoleOr(roles)
  const hasRolesAnd = useHasRoleAnd(roles)

  // 计算权限验证结果
  let hasPermission = true
  if (permission) {
    hasPermission = hasSinglePermission
  } else if (permissions.length > 0) {
    hasPermission =
      permissionLogic === 'or' ? hasPermissionsOr : hasPermissionsAnd
  }

  // 计算角色验证结果
  let hasRole = true
  if (role) {
    hasRole = hasSingleRole
  } else if (roles.length > 0) {
    hasRole = roleLogic === 'or' ? hasRolesOr : hasRolesAnd
  }

  // 组合验证结果
  const hasAccess =
    combineLogic === 'or' ? hasPermission || hasRole : hasPermission && hasRole

  return hasAccess ? children : fallback
}

// 使用示例：

/*
// 1. 单个权限控制
<PermissionGuard permission="user:create">
  <button>创建用户</button>
</PermissionGuard>

// 2. 多个权限控制（或关系）
<PermissionGuardOr permissions={['user:create', 'user:update']}>
  <button>用户操作</button>
</PermissionGuardOr>

// 3. 多个权限控制（且关系）
<PermissionGuardAnd permissions={['user:create', 'admin:access']}>
  <button>高级操作</button>
</PermissionGuardAnd>

// 4. 角色控制
<RoleGuard role="admin">
  <div>管理员面板</div>
</RoleGuard>

// 5. 多个角色控制
<RoleGuardOr roles={['admin', 'moderator']}>
  <div>管理功能</div>
</RoleGuardOr>

// 6. 综合权限控制
<AuthGuard 
  permissions={['user:create', 'user:update']} 
  permissionLogic="or"
  roles={['admin', 'manager']} 
  roleLogic="or"
  combineLogic="and"
  fallback={<div>无权限访问</div>}
>
  <div>需要权限的内容</div>
</AuthGuard>

// 7. 带回退内容的权限控制
<PermissionGuard 
  permission="user:delete" 
  fallback={<span style={{color: 'gray'}}>删除(无权限)</span>}
>
  <button style={{color: 'red'}}>删除</button>
</PermissionGuard>
*/
