import { Descriptions } from 'antd'

// STR检测组件
function STRTestComponent({ data }) {
  return (
    <Descriptions
      column={3}
      size="small"
      items={[
        {
          key: 'strProfile',
          label: 'STR图谱',
          children: data?.strProfile || '-',
        },
        {
          key: 'matchResult',
          label: '匹配结果',
          children: data?.matchResult || '-',
        },
        {
          key: 'testMethod',
          label: '检测方法',
          children: data?.testMethod || '-',
        },
      ]}
    />
  )
}

export default STRTestComponent
