import { defineMock } from 'vite-plugin-mock-dev-server'

// 生成模拟的审批数据
const generateMockApprovalData = (type, count = 50) => {
  const data = []
  const statusOptions = [0, 1, 2] // 0: 待审批, 1: 审批通过, 2: 审批驳回
  const cellSources = ['肝脏', '肺部', '肾脏', '心脏', '脑部', '胃部']
  const cellTypes = ['悬浮', '贴壁']
  const libraryLevels = ['一级库', '二级库', '三级库']
  const outboundPurposes = [
    '科研使用',
    '临床试验',
    '质量检测',
    '技术开发',
    '合作研究',
  ]
  const outboundFlags = ['正常出库', '紧急出库', '临时出库']
  const materialCategories = ['细胞']
  const businessTypes = [
    '细胞培养',
    '细胞检测',
    '细胞保存',
    '细胞扩增',
    '细胞分析',
  ]
  const materialTypes = ['培养基', '血清', '试剂', '耗材', '设备']
  const orderSources = ['内部申请', '外部委托', '合作项目', '科研项目']

  for (let i = 0; i < count; i++) {
    const status =
      statusOptions[Math.floor(Math.random() * statusOptions.length)]
    const cellSource =
      cellSources[Math.floor(Math.random() * cellSources.length)]
    const cellType = cellTypes[Math.floor(Math.random() * cellTypes.length)]
    const libraryLevel =
      libraryLevels[Math.floor(Math.random() * libraryLevels.length)]
    const outboundPurpose =
      outboundPurposes[Math.floor(Math.random() * outboundPurposes.length)]
    const outboundFlag =
      outboundFlags[Math.floor(Math.random() * outboundFlags.length)]

    const baseData = {
      bid: `${type}_${String(i + 1).padStart(3, '0')}`,
      id: i + 1,
      status,
      approvalType: type,
      stockInNo: `RK${String(i + 1).padStart(6, '0')}`,
      cellCode: `CC${String(i + 1).padStart(4, '0')}`,
      cellNameCn: `细胞株${String.fromCharCode(65 + (i % 26))}`,
      cellNameEn: `Cell Line ${String.fromCharCode(65 + (i % 26))}`,
      sourceOrgan: cellSource,
      storageLibraryLevel: libraryLevel,
      originalBatchNo: `OB${String(i + 1).padStart(4, '0')}`,
      cryoBatchNo: `FB${String(i + 1).padStart(4, '0')}`,
      currentNum: Math.floor(Math.random() * 100) + 10,
      cellGeneration: Math.floor(Math.random() * 50) + 1,
      cellType,
      medium: 'DMEM + 10% FBS',
      cultureCondition: '37℃, 5% CO2',
      recoveryTime: `2025-01-${String(Math.floor(Math.random() * 15) + 1).padStart(2, '0')}T${String(Math.floor(Math.random() * 12) + 8).padStart(2, '0')}:${String(Math.floor(Math.random() * 60)).padStart(2, '0')}:00`,
      recoveryViability: `${Math.floor(Math.random() * 20) + 80}%`,
      remarks: i % 3 === 0 ? `备注信息${i + 1}` : '',
      tubeNum: Math.floor(Math.random() * 50) + 10,
      checkHistory: Math.floor(Math.random() * 5) + 1,
      analysisItemText:
        type === 'quality'
          ? 'STR,支原体PCR'
          : type === 'amplification'
            ? '细胞活力检测'
            : type === 'outbound'
              ? '出库检验'
              : '材料检验',
      strFlag: status >= 1 ? '是' : '待检测',
      strRemark: '',
      mycoPcr: status >= 1 ? '阴性' : '待检测',
      mycoCulture: status >= 1 ? '阴性' : '待检测',
      mycoDna: status >= 1 ? '阴性' : '待检测',
      speciesPcr: status >= 1 ? '人源' : '待检测',
      fungusLowTemp: status >= 1 ? '阴性' : '待检测',
      fungusHighTemp: status >= 1 ? '阴性' : '待检测',
      dnaBarcoding: status >= 1 ? '正常' : '待检测',
      applierName: `申请人${String.fromCharCode(65 + (i % 26))}`,
      stockInTime: `2025-01-${String(Math.floor(Math.random() * 15) + 1).padStart(2, '0')}T${String(Math.floor(Math.random() * 12) + 8).padStart(2, '0')}:${String(Math.floor(Math.random() * 60)).padStart(2, '0')}:00`,
      stockInUser: `入库员${String.fromCharCode(65 + (i % 10))}`,
    }

    // 为出库审批添加特有字段
    if (type === 'outbound') {
      baseData.outboundNo = `CK${String(i + 1).padStart(6, '0')}`
      baseData.outboundNum = Math.floor(Math.random() * 20) + 1
      baseData.outboundUser = `出库员${String.fromCharCode(65 + (i % 10))}`
      baseData.outboundPurpose = outboundPurpose
      baseData.outboundFlag = outboundFlag
      baseData.frozenNum = Math.floor(Math.random() * 30) + 10
      baseData.recoveryNum = Math.floor(Math.random() * 10) + 1
    }

    // 为材料审批添加特有字段
    if (type === 'material') {
      baseData.orderNo = `DD${String(i + 1).padStart(6, '0')}`
      baseData.category =
        materialCategories[
          Math.floor(Math.random() * materialCategories.length)
        ]
      baseData.business =
        businessTypes[Math.floor(Math.random() * businessTypes.length)]
      baseData.material =
        materialTypes[Math.floor(Math.random() * materialTypes.length)]
      baseData.orderSource =
        orderSources[Math.floor(Math.random() * orderSources.length)]
    }

    // 添加通用字段
    baseData.approverName = status >= 1 ? '审核员' : ''
    baseData.checkTime =
      status >= 1
        ? `2025-01-${String(Math.floor(Math.random() * 15) + 1).padStart(2, '0')}T${String(Math.floor(Math.random() * 12) + 8).padStart(2, '0')}:${String(Math.floor(Math.random() * 60)).padStart(2, '0')}:00`
        : null
    baseData.createdAt = `2025-01-${String(Math.floor(Math.random() * 15) + 1).padStart(2, '0')}T${String(Math.floor(Math.random() * 12) + 8).padStart(2, '0')}:${String(Math.floor(Math.random() * 60)).padStart(2, '0')}:00`
    baseData.approveTime =
      status >= 1
        ? `2025-01-${String(Math.floor(Math.random() * 15) + 1).padStart(2, '0')}T${String(Math.floor(Math.random() * 12) + 8).padStart(2, '0')}:${String(Math.floor(Math.random() * 60)).padStart(2, '0')}:00`
        : null
    baseData.rejectReason = status === 2 ? '检测条件不符合要求' : ''

    data.push(baseData)
  }
  return data
}

// 生成各类型的模拟数据
const mockQualityApprovalData = generateMockApprovalData('quality', 30)
const mockAmplificationApprovalData = generateMockApprovalData(
  'amplification',
  25,
)
const mockOutboundApprovalData = generateMockApprovalData('outbound', 35)
const mockMaterialApprovalData = generateMockApprovalData('material', 20)

const myApprovalMock = defineMock(
  {
    // 获取审批概览数据
    '[GET]/approval/overview': () => {
      const calculateCounts = (data) => {
        return {
          total: data.length,
          pending: data.filter((item) => item.status === 0).length,
          approved: data.filter((item) => item.status === 1).length,
          rejected: data.filter((item) => item.status === 2).length,
        }
      }

      return {
        code: 0,
        data: {
          quality: calculateCounts(mockQualityApprovalData),
          amplification: calculateCounts(mockAmplificationApprovalData),
          outbound: calculateCounts(mockOutboundApprovalData),
          material: calculateCounts(mockMaterialApprovalData),
        },
        msg: '成功',
      }
    },

    // 质检审批列表
    '[POST]/approval/quality/list': ({ data }) => {
      const requestData = data
      const page = requestData?.page?.pageNum || 1
      const pageSize = requestData?.page?.pageSize || 10
      const params = requestData?.params || {}

      let filteredData = [...mockQualityApprovalData]

      // 根据查询条件过滤
      if (params.stockInNo) {
        filteredData = filteredData.filter((item) =>
          item.stockInNo.includes(params.stockInNo),
        )
      }

      if (params.cellCode) {
        filteredData = filteredData.filter((item) =>
          item.cellCode.includes(params.cellCode),
        )
      }

      if (params.cellName) {
        filteredData = filteredData.filter(
          (item) =>
            item.cellNameCn.includes(params.cellName) ||
            item.cellNameEn.includes(params.cellName),
        )
      }

      if (params.sourceOrgan) {
        filteredData = filteredData.filter((item) =>
          item.sourceOrgan.includes(params.sourceOrgan),
        )
      }

      if (params.originalBatchNo) {
        filteredData = filteredData.filter((item) =>
          item.originalBatchNo.includes(params.originalBatchNo),
        )
      }

      if (params.cryoBatchNo) {
        filteredData = filteredData.filter((item) =>
          item.cryoBatchNo.includes(params.cryoBatchNo),
        )
      }

      if (params.storageLibraryLevel) {
        filteredData = filteredData.filter(
          (item) => item.storageLibraryLevel === params.storageLibraryLevel,
        )
      }

      // 时间范围过滤
      if (params.createdAtStart && params.createdAtEnd) {
        filteredData = filteredData.filter((item) => {
          if (!item.createdAt) return false
          const createdAt = new Date(item.createdAt)
          const startTime = new Date(params.createdAtStart)
          const endTime = new Date(params.createdAtEnd)
          return createdAt >= startTime && createdAt <= endTime
        })
      }

      // 计算分页数据
      const start = (page - 1) * pageSize
      const end = start + pageSize
      const _data = filteredData.slice(start, end)

      return {
        code: 0,
        data: {
          page,
          pageSize,
          total: filteredData.length,
          list: _data,
          hasNext: end < filteredData.length,
        },
        msg: '成功',
      }
    },

    // 扩增审批列表
    '[POST]/approval/amplification/list': ({ data }) => {
      const requestData = data
      const page = requestData?.page?.pageNum || 1
      const pageSize = requestData?.page?.pageSize || 10
      const params = requestData?.params || {}

      let filteredData = [...mockAmplificationApprovalData]

      // 根据查询条件过滤
      if (params.stockInNo) {
        filteredData = filteredData.filter((item) =>
          item.stockInNo.includes(params.stockInNo),
        )
      }

      if (params.cellCode) {
        filteredData = filteredData.filter((item) =>
          item.cellCode.includes(params.cellCode),
        )
      }

      if (params.cellName) {
        filteredData = filteredData.filter(
          (item) =>
            item.cellNameCn.includes(params.cellName) ||
            item.cellNameEn.includes(params.cellName),
        )
      }

      if (params.sourceOrgan) {
        filteredData = filteredData.filter((item) =>
          item.sourceOrgan.includes(params.sourceOrgan),
        )
      }

      if (params.originalBatchNo) {
        filteredData = filteredData.filter((item) =>
          item.originalBatchNo.includes(params.originalBatchNo),
        )
      }

      if (params.cryoBatchNo) {
        filteredData = filteredData.filter((item) =>
          item.cryoBatchNo.includes(params.cryoBatchNo),
        )
      }

      if (params.storageLibraryLevel) {
        filteredData = filteredData.filter(
          (item) => item.storageLibraryLevel === params.storageLibraryLevel,
        )
      }

      // 时间范围过滤
      if (params.createdAtStart && params.createdAtEnd) {
        filteredData = filteredData.filter((item) => {
          if (!item.createdAt) return false
          const createdAt = new Date(item.createdAt)
          const startTime = new Date(params.createdAtStart)
          const endTime = new Date(params.createdAtEnd)
          return createdAt >= startTime && createdAt <= endTime
        })
      }

      const start = (page - 1) * pageSize
      const end = start + pageSize
      const _data = filteredData.slice(start, end)

      return {
        code: 0,
        data: {
          page,
          pageSize,
          total: filteredData.length,
          list: _data,
          hasNext: end < filteredData.length,
        },
        msg: '成功',
      }
    },

    // 出库审批列表
    '[POST]/approval/outbound/list': ({ data }) => {
      const requestData = data
      const page = requestData?.page?.pageNum || 1
      const pageSize = requestData?.page?.pageSize || 10
      const params = requestData?.params || {}

      let filteredData = [...mockOutboundApprovalData].filter(
        (item) => item.status === 0,
      ) // 只显示待审批

      // 过滤逻辑
      if (params.outboundNo) {
        filteredData = filteredData.filter((item) =>
          item.outboundNo?.includes(params.outboundNo),
        )
      }
      if (params.stockInNo) {
        filteredData = filteredData.filter((item) =>
          item.stockInNo.includes(params.stockInNo),
        )
      }
      if (params.cellCode) {
        filteredData = filteredData.filter((item) =>
          item.cellCode.includes(params.cellCode),
        )
      }
      if (params.cellName) {
        filteredData = filteredData.filter(
          (item) =>
            item.cellNameCn?.includes(params.cellName) ||
            item.cellNameEn?.includes(params.cellName),
        )
      }
      if (params.sourceOrgan) {
        filteredData = filteredData.filter((item) =>
          item.sourceOrgan.includes(params.sourceOrgan),
        )
      }
      if (params.originalBatchNo) {
        filteredData = filteredData.filter((item) =>
          item.originalBatchNo.includes(params.originalBatchNo),
        )
      }
      if (params.cryoBatchNo) {
        filteredData = filteredData.filter((item) =>
          item.cryoBatchNo.includes(params.cryoBatchNo),
        )
      }
      if (params.storageLibraryLevel) {
        filteredData = filteredData.filter(
          (item) => item.storageLibraryLevel === params.storageLibraryLevel,
        )
      }
      if (params.createdAtStart && params.createdAtEnd) {
        filteredData = filteredData.filter((item) => {
          const itemDate = new Date(item.createdAt)
          const startDate = new Date(params.createdAtStart)
          const endDate = new Date(params.createdAtEnd)
          return itemDate >= startDate && itemDate <= endDate
        })
      }

      const start = (page - 1) * pageSize
      const end = start + pageSize
      const _data = filteredData.slice(start, end)

      return {
        code: 0,
        data: {
          page,
          pageSize,
          total: filteredData.length,
          list: _data,
          hasNext: end < filteredData.length,
        },
        msg: '成功',
      }
    },

    // 出库审批记录列表
    '[POST]/approval/outbound/record/list': ({ data }) => {
      const requestData = data
      const page = requestData?.page?.pageNum || 1
      const pageSize = requestData?.page?.pageSize || 10
      const params = requestData?.params || {}

      let filteredData = [...mockOutboundApprovalData].filter(
        (item) => item.status !== 0,
      ) // 只显示已审批

      // 过滤逻辑（与待审批相同）
      if (params.outboundNo) {
        filteredData = filteredData.filter((item) =>
          item.outboundNo?.includes(params.outboundNo),
        )
      }
      if (params.stockInNo) {
        filteredData = filteredData.filter((item) =>
          item.stockInNo.includes(params.stockInNo),
        )
      }
      if (params.cellCode) {
        filteredData = filteredData.filter((item) =>
          item.cellCode.includes(params.cellCode),
        )
      }
      if (params.cellName) {
        filteredData = filteredData.filter(
          (item) =>
            item.cellNameCn?.includes(params.cellName) ||
            item.cellNameEn?.includes(params.cellName),
        )
      }
      if (params.sourceOrgan) {
        filteredData = filteredData.filter((item) =>
          item.sourceOrgan.includes(params.sourceOrgan),
        )
      }
      if (params.originalBatchNo) {
        filteredData = filteredData.filter((item) =>
          item.originalBatchNo.includes(params.originalBatchNo),
        )
      }
      if (params.cryoBatchNo) {
        filteredData = filteredData.filter((item) =>
          item.cryoBatchNo.includes(params.cryoBatchNo),
        )
      }
      if (params.storageLibraryLevel) {
        filteredData = filteredData.filter(
          (item) => item.storageLibraryLevel === params.storageLibraryLevel,
        )
      }
      if (params.createdAtStart && params.createdAtEnd) {
        filteredData = filteredData.filter((item) => {
          const itemDate = new Date(item.createdAt)
          const startDate = new Date(params.createdAtStart)
          const endDate = new Date(params.createdAtEnd)
          return itemDate >= startDate && itemDate <= endDate
        })
      }

      const start = (page - 1) * pageSize
      const end = start + pageSize
      const _data = filteredData.slice(start, end)

      return {
        code: 0,
        data: {
          page,
          pageSize,
          total: filteredData.length,
          list: _data,
          hasNext: end < filteredData.length,
        },
        msg: '成功',
      }
    },

    // 材料审批列表（待审批）
    '[POST]/approval/material/list': ({ data }) => {
      const requestData = data
      const page = requestData?.page?.pageNum || 1
      const pageSize = requestData?.page?.pageSize || 10
      const params = requestData?.params || {}

      let filteredData = [...mockMaterialApprovalData].filter(
        (item) => item.status === 0,
      )

      // 过滤逻辑
      if (params.orderNo) {
        filteredData = filteredData.filter((item) =>
          item.orderNo?.includes(params.orderNo),
        )
      }
      if (params.category) {
        filteredData = filteredData.filter(
          (item) => item.category === params.category,
        )
      }
      if (params.business) {
        filteredData = filteredData.filter(
          (item) => item.business === params.business,
        )
      }
      if (params.material) {
        filteredData = filteredData.filter(
          (item) => item.material === params.material,
        )
      }
      if (params.applierName) {
        filteredData = filteredData.filter((item) =>
          item.applierName?.includes(params.applierName),
        )
      }
      if (params.createdAt && params.createdAt.length === 2) {
        const [startTime, endTime] = params.createdAt
        filteredData = filteredData.filter((item) => {
          const itemTime = new Date(item.createdAt)
          return (
            itemTime >= new Date(startTime) && itemTime <= new Date(endTime)
          )
        })
      }

      const start = (page - 1) * pageSize
      const end = start + pageSize
      const _data = filteredData.slice(start, end)

      return {
        code: 0,
        data: {
          page,
          pageSize,
          total: filteredData.length,
          list: _data,
          hasNext: end < filteredData.length,
        },
        msg: '成功',
      }
    },

    // 材料审批记录列表
    '[POST]/approval/material/record/list': ({ data }) => {
      const requestData = data
      const page = requestData?.page?.pageNum || 1
      const pageSize = requestData?.page?.pageSize || 10
      const params = requestData?.params || {}

      let filteredData = [...mockMaterialApprovalData].filter(
        (item) => item.status !== 0,
      )

      // 过滤逻辑
      if (params.orderNo) {
        filteredData = filteredData.filter((item) =>
          item.orderNo?.includes(params.orderNo),
        )
      }
      if (params.category) {
        filteredData = filteredData.filter(
          (item) => item.category === params.category,
        )
      }
      if (params.business) {
        filteredData = filteredData.filter(
          (item) => item.business === params.business,
        )
      }
      if (params.material) {
        filteredData = filteredData.filter(
          (item) => item.material === params.material,
        )
      }
      if (params.applierName) {
        filteredData = filteredData.filter((item) =>
          item.applierName?.includes(params.applierName),
        )
      }
      if (params.approveTime && params.approveTime.length === 2) {
        const [startTime, endTime] = params.approveTime
        filteredData = filteredData.filter((item) => {
          if (!item.approveTime) return false
          const itemTime = new Date(item.approveTime)
          return (
            itemTime >= new Date(startTime) && itemTime <= new Date(endTime)
          )
        })
      }
      if (params.createdAt && params.createdAt.length === 2) {
        const [startTime, endTime] = params.createdAt
        filteredData = filteredData.filter((item) => {
          const itemTime = new Date(item.createdAt)
          return (
            itemTime >= new Date(startTime) && itemTime <= new Date(endTime)
          )
        })
      }

      const start = (page - 1) * pageSize
      const end = start + pageSize
      const _data = filteredData.slice(start, end)

      return {
        code: 0,
        data: {
          page,
          pageSize,
          total: filteredData.length,
          list: _data,
          hasNext: end < filteredData.length,
        },
        msg: '成功',
      }
    },

    // 审批通过
    '[POST]/approval/pass': ({ data }) => {
      const { approvalBid } = data

      // 在所有数据中查找并更新状态
      const allData = [
        ...mockQualityApprovalData,
        ...mockAmplificationApprovalData,
        ...mockOutboundApprovalData,
        ...mockMaterialApprovalData,
      ]

      const record = allData.find((item) => item.bid === approvalBid)
      if (record) {
        record.status = 1 // 审批通过
        record.approverName = '当前审批员'
        record.approveTime = new Date().toISOString()
      }

      return {
        code: 0,
        data: {
          message: '审批通过成功',
        },
        msg: '成功',
      }
    },

    // 审批驳回
    '[POST]/approval/reject': ({ data }) => {
      const { approvalBid, rejectReason } = data

      // 在所有数据中查找并更新状态
      const allData = [
        ...mockQualityApprovalData,
        ...mockAmplificationApprovalData,
        ...mockOutboundApprovalData,
        ...mockMaterialApprovalData,
      ]

      const record = allData.find((item) => item.bid === approvalBid)
      if (record) {
        record.status = 2 // 审批驳回
        record.approverName = '当前审批员'
        record.approveTime = new Date().toISOString()
        record.rejectReason = rejectReason
      }

      return {
        code: 0,
        data: {
          message: '审批驳回成功',
        },
        msg: '成功',
      }
    },

    // 获取审批详情
    '[GET]/approval/detail': ({ query }) => {
      const { approvalBid } = query

      const allData = [
        ...mockQualityApprovalData,
        ...mockAmplificationApprovalData,
        ...mockOutboundApprovalData,
        ...mockMaterialApprovalData,
      ]

      const record = allData.find((item) => item.bid === approvalBid)

      if (record) {
        return {
          code: 0,
          data: record,
          msg: '成功',
        }
      } else {
        return {
          code: 404,
          data: null,
          msg: '数据不存在',
        }
      }
    },

    // 导出审批清单
    '[POST]/approval/export': () => {
      // 模拟导出进度
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve({
            code: 0,
            data: {
              downloadUrl: '/mock/approval-list.xlsx',
              fileName: '审批清单.xlsx',
            },
            msg: '导出成功',
          })
        }, 2000)
      })
    },
  },
  true,
)

export default myApprovalMock
