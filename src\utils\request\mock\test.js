import { defineMock } from '@alova/mock'

// 模拟数据 - 假设有100条待办事项
const mockTodos = [
  {
    id: 624748504,
    number: 6689,
    title: '🐛 [BUG]yarn install命令 antd2.4.5会报错',
    labels: [
      {
        name: 'bug',
        color: 'error',
      },
    ],
    state: 'open',
    locked: false,
    comments: 1,
    created_at: 1590486176000,
    updated_at: 1590487382000,
    closed_at: null,
    author_association: 'NONE',
    user: 'chenshuai2144',
    avatar:
      'https://gw.alipayobjects.com/zos/antfincdn/XAosXuNZyF/BiazfanxmamNRoxxVxka.png',
  },
  {
    id: 624691229,
    number: 6688,
    title: '🐛 [BUG]无法创建工程npm create umi',
    labels: [
      {
        name: 'bug',
        color: 'error',
      },
    ],
    state: 'open',
    locked: false,
    comments: 0,
    created_at: 1590481162000,
    updated_at: 1590481162000,
    closed_at: null,
    author_association: 'NONE',
    user: 'chenshuai2144',
    avatar:
      'https://gw.alipayobjects.com/zos/antfincdn/XAosXuNZyF/BiazfanxmamNRoxxVxka.png',
  },
  {
    id: 624674790,
    number: 6685,
    title: '🧐 [问题] build 后还存在 es6 的代码（Umi@2.13.13）',
    labels: [
      {
        name: 'question',
        color: 'success',
      },
    ],
    state: 'open',
    locked: false,
    comments: 0,
    created_at: 1590479665000,
    updated_at: 1590479665000,
    closed_at: null,
    author_association: 'NONE',
    user: 'chenshuai2144',
    avatar:
      'https://gw.alipayobjects.com/zos/antfincdn/XAosXuNZyF/BiazfanxmamNRoxxVxka.png',
  },
  {
    id: 624620220,
    number: 6683,
    title: '2.3.1版本如何在业务页面修改头部状态',
    labels: [
      {
        name: 'question',
        color: 'success',
      },
    ],
    state: 'open',
    locked: false,
    comments: 2,
    created_at: 1590472704000,
    updated_at: 1590477459000,
    closed_at: null,
    author_association: 'NONE',
    user: 'chenshuai2144',
    avatar:
      'https://gw.alipayobjects.com/zos/antfincdn/XAosXuNZyF/BiazfanxmamNRoxxVxka.png',
  },
  {
    id: 624592471,
    number: 6682,
    title: 'hideChildrenInMenu设置后，子路由找不到了',
    labels: [
      {
        name: 'bug',
        color: 'error',
      },
    ],
    state: 'open',
    locked: false,
    comments: 2,
    created_at: 1590467159000,
    updated_at: 1590480051000,
    closed_at: null,
    author_association: 'NONE',
    user: 'chenshuai2144',
    avatar:
      'https://gw.alipayobjects.com/zos/antfincdn/XAosXuNZyF/BiazfanxmamNRoxxVxka.png',
  },
  {
    id: 624556297,
    number: 6680,
    title: '🐛 [BUG]Umi UI 添加多个空白页，就会出错！把空白页都变成选中状态！',
    labels: [
      {
        name: 'bug',
        color: 'error',
      },
    ],
    state: 'open',
    locked: false,
    comments: 0,
    created_at: 1590459227000,
    updated_at: 1590459227000,
    closed_at: null,
    author_association: 'NONE',
    user: 'chenshuai2144',
    avatar:
      'https://gw.alipayobjects.com/zos/antfincdn/XAosXuNZyF/BiazfanxmamNRoxxVxka.png',
  },
  {
    id: 624415799,
    number: 6678,
    title: '🐛 [BUG]第一次载入页面，菜单仅图标时，图标没有居中',
    labels: [
      {
        name: 'bug',
        color: 'error',
      },
    ],
    state: 'open',
    locked: false,
    comments: 1,
    created_at: 1590428061000,
    updated_at: 1590462355000,
    closed_at: null,
    author_association: 'NONE',
    user: 'chenshuai2144',
    avatar:
      'https://gw.alipayobjects.com/zos/antfincdn/XAosXuNZyF/BiazfanxmamNRoxxVxka.png',
  },
  {
    id: 624300343,
    number: 6675,
    title: 'build(deps-dev): bump eslint from 6.8.0 to 7.1.0',
    labels: [
      {
        name: 'dependencies',
        color: 'default',
      },
    ],
    state: 'open',
    locked: false,
    comments: 0,
    created_at: 1590413229000,
    updated_at: 1590413230000,
    closed_at: null,
    author_association: 'CONTRIBUTOR',
    pull_request: {
      url: 'https://api.github.com/repos/ant-design/ant-design-pro/pulls/6675',
      html_url: 'https://github.com/ant-design/ant-design-pro/pull/6675',
      diff_url: 'https://github.com/ant-design/ant-design-pro/pull/6675.diff',
      patch_url: 'https://github.com/ant-design/ant-design-pro/pull/6675.patch',
    },
    user: 'chenshuai2144',
    avatar:
      'https://gw.alipayobjects.com/zos/antfincdn/XAosXuNZyF/BiazfanxmamNRoxxVxka.png',
  },
  {
    id: 624130987,
    number: 6674,
    title: '🧐 [问题] V4版本如何使用第三方的enhanceReduxMiddleware',
    labels: [
      {
        name: 'question',
        color: 'success',
      },
    ],
    state: 'open',
    locked: false,
    comments: 3,
    created_at: 1590394831000,
    updated_at: 1590478667000,
    closed_at: null,
    author_association: 'NONE',
    user: 'chenshuai2144',
    avatar:
      'https://gw.alipayobjects.com/zos/antfincdn/XAosXuNZyF/BiazfanxmamNRoxxVxka.png',
  },
  {
    id: 623677811,
    number: 6663,
    title:
      '🐛 [BUG] 官网预览页面，第一次点击二级菜单，其父级菜单会收起，之后再次点击二级菜单，父菜单正常',
    state: 'open',
    locked: false,
    comments: 1,
    created_at: 1590246049000,
    updated_at: 1590364057000,
    closed_at: null,
    author_association: 'NONE',
    user: 'chenshuai2144',
    avatar:
      'https://gw.alipayobjects.com/zos/antfincdn/XAosXuNZyF/BiazfanxmamNRoxxVxka.png',
    labels: [
      {
        name: 'question',
        color: 'processing',
      },
    ],
  },
  {
    id: 623565176,
    number: 6662,
    title: '🧐 [问题] 从自建 block 仓库下载区块报错。',
    labels: [
      {
        name: 'question',
        color: 'success',
      },
    ],
    state: 'open',
    locked: false,
    comments: 0,
    created_at: 1590201972000,
    updated_at: 1590203006000,
    closed_at: null,
    author_association: 'CONTRIBUTOR',
    user: 'chenshuai2144',
    avatar:
      'https://gw.alipayobjects.com/zos/antfincdn/XAosXuNZyF/BiazfanxmamNRoxxVxka.png',
  },
  {
    id: 622902420,
    number: 6652,
    title:
      '🧐 [问题] fetchCurrent接口报错，退出登录页，第一次点击登录，SecurityLayout不渲染，导致需要点击两次',
    labels: [
      {
        name: 'question',
        color: 'success',
      },
    ],
    state: 'open',
    locked: false,
    comments: 0,
    created_at: 1590114027000,
    updated_at: 1590114061000,
    closed_at: null,
    author_association: 'NONE',
    user: 'chenshuai2144',
    avatar:
      'https://gw.alipayobjects.com/zos/antfincdn/XAosXuNZyF/BiazfanxmamNRoxxVxka.png',
  },
  {
    id: 622348582,
    number: 6644,
    title: '🐛 [BUG] V5 左侧栏收缩时，点击图标无效。',
    labels: [
      {
        name: 'bug',
        color: 'error',
      },
    ],
    state: 'open',
    locked: false,
    comments: 0,
    created_at: 1590050713000,
    updated_at: 1590050713000,
    closed_at: null,
    author_association: 'CONTRIBUTOR',
    user: 'chenshuai2144',
    avatar:
      'https://gw.alipayobjects.com/zos/antfincdn/XAosXuNZyF/BiazfanxmamNRoxxVxka.png',
  },
  {
    id: 622326186,
    number: 6643,
    title: '🧐 [问题]不知道有没有大佬将这个模板迁移至Electron的例子',
    labels: [
      {
        name: 'question',
        color: 'success',
      },
    ],
    state: 'open',
    locked: false,
    comments: 0,
    created_at: 1590048276000,
    updated_at: 1590048276000,
    closed_at: null,
    author_association: 'NONE',
    user: 'chenshuai2144',
    avatar:
      'https://gw.alipayobjects.com/zos/antfincdn/XAosXuNZyF/BiazfanxmamNRoxxVxka.png',
  },
  {
    id: 622290419,
    number: 6642,
    title: 'npm run start 为什么不能打开浏览器',
    labels: [
      {
        name: 'bug',
        color: 'error',
      },
    ],
    state: 'open',
    locked: false,
    comments: 3,
    created_at: 1590043882000,
    updated_at: 1590364319000,
    closed_at: null,
    author_association: 'NONE',
    user: 'chenshuai2144',
    avatar:
      'https://gw.alipayobjects.com/zos/antfincdn/XAosXuNZyF/BiazfanxmamNRoxxVxka.png',
  },
  {
    id: 622267649,
    number: 6641,
    title:
      '🧐 [问题]在重新npm install后运行npm start报出一些less找不到，但项目可以正常运行起来',
    labels: [
      {
        name: 'question',
        color: 'success',
      },
    ],
    state: 'open',
    locked: false,
    comments: 3,
    created_at: 1590040596000,
    updated_at: 1590111510000,
    closed_at: null,
    author_association: 'NONE',
    user: 'chenshuai2144',
    avatar:
      'https://gw.alipayobjects.com/zos/antfincdn/XAosXuNZyF/BiazfanxmamNRoxxVxka.png',
  },
  {
    id: 622207575,
    number: 6639,
    title: '🐛 [BUG]错误通知：http code 200',
    labels: [
      {
        name: 'bug',
        color: 'error',
      },
    ],
    state: 'open',
    locked: false,
    comments: 4,
    created_at: 1590029255000,
    updated_at: 1590337620000,
    closed_at: null,
    author_association: 'NONE',
    user: 'chenshuai2144',
    avatar:
      'https://gw.alipayobjects.com/zos/antfincdn/XAosXuNZyF/BiazfanxmamNRoxxVxka.png',
  },
  {
    id: 621402301,
    number: 6630,
    title: '🐛 [BUG]线上预览项目好多布局错乱，不知道是antd的锅还是啥原因',
    labels: [
      {
        name: 'In Progress',
        color: 'processing',
      },
    ],
    state: 'open',
    locked: false,
    comments: 8,
    created_at: 1589940153000,
    updated_at: 1589962404000,
    closed_at: null,
    author_association: 'NONE',
    user: 'chenshuai2144',
    avatar:
      'https://gw.alipayobjects.com/zos/antfincdn/XAosXuNZyF/BiazfanxmamNRoxxVxka.png',
  },
  {
    id: 621388407,
    number: 6629,
    title: '🐛 [BUG] umi 偶尔出现没有导出成员',
    labels: [
      {
        name: 'bug',
        color: 'error',
      },
    ],
    state: 'open',
    locked: false,
    comments: 19,
    created_at: 1589937655000,
    updated_at: 1590364383000,
    closed_at: null,
    author_association: 'CONTRIBUTOR',
    user: 'chenshuai2144',
    avatar:
      'https://gw.alipayobjects.com/zos/antfincdn/XAosXuNZyF/BiazfanxmamNRoxxVxka.png',
  },
  {
    id: 620820348,
    number: 6624,
    title:
      '🐛 [BUG]请问大佬，为什么无论怎么选择，都无法切换成JS语言，怎么下都是TS,求解答',
    labels: [
      {
        name: 'bug',
        color: 'error',
      },
    ],
    state: 'open',
    locked: false,
    comments: 6,
    created_at: 1589880167000,
    updated_at: 1590378654000,
    closed_at: null,
    author_association: 'NONE',
    user: 'chenshuai2144',
    avatar:
      'https://gw.alipayobjects.com/zos/antfincdn/XAosXuNZyF/BiazfanxmamNRoxxVxka.png',
  },
  {
    id: 620673679,
    number: 6619,
    title: '🐛 [BUG] protable内存泄漏问题',
    labels: [
      {
        name: 'In Progress',
        color: 'processing',
      },
    ],
    state: 'open',
    locked: false,
    comments: 9,
    created_at: 1589863527000,
    updated_at: 1589947582000,
    closed_at: null,
    author_association: 'NONE',
    url: 'https://github.com/ant-design/ant-design-pro/issues/6619',
    user: 'chenshuai2144',
    avatar:
      'https://gw.alipayobjects.com/zos/antfincdn/XAosXuNZyF/BiazfanxmamNRoxxVxka.png',
  },
  {
    id: 620666530,
    number: 6618,
    title:
      'ProTable中选择开始日期和结束日期，判断结束日期大于开始日期，有没有选择完日期后的回调？',
    labels: [
      {
        name: 'question',
        color: 'success',
      },
    ],
    state: 'open',
    locked: false,
    comments: 0,
    created_at: 1589862267000,
    updated_at: 1589862267000,
    closed_at: null,
    author_association: 'NONE',
    url: 'https://github.com/ant-design/ant-design-pro/issues/6618',
    user: 'chenshuai2144',
    avatar:
      'https://gw.alipayobjects.com/zos/antfincdn/XAosXuNZyF/BiazfanxmamNRoxxVxka.png',
  },
  {
    id: 620313393,
    number: 6614,
    title: '🧐 [问题]有人用过在这个上集成IOC吗?',
    labels: [
      {
        name: 'question',
        color: 'success',
      },
    ],
    state: 'open',
    locked: false,
    comments: 0,
    created_at: 1589816693000,
    updated_at: 1589855611000,
    closed_at: null,
    author_association: 'NONE',
    url: 'https://github.com/ant-design/ant-design-pro/issues/6614',
    user: 'chenshuai2144',
    avatar:
      'https://gw.alipayobjects.com/zos/antfincdn/XAosXuNZyF/BiazfanxmamNRoxxVxka.png',
  },
  {
    id: 619865001,
    number: 6605,
    title: 'Ant Design Pro V5 已经支持预览',
    labels: [
      {
        name: 'question',
        color: 'success',
      },
    ],
    state: 'open',
    locked: false,
    comments: 76,
    created_at: 1589769224000,
    updated_at: 1590479999000,
    closed_at: null,
    author_association: 'COLLABORATOR',
    url: 'https://github.com/ant-design/ant-design-pro/issues/6605',
    user: 'chenshuai2144',
    avatar:
      'https://gw.alipayobjects.com/zos/antfincdn/XAosXuNZyF/BiazfanxmamNRoxxVxka.png',
  },
  {
    id: 619494862,
    number: 6599,
    title: 'Add support for RTL ',
    labels: [
      {
        name: 'Feature Request',
        color: 'default',
      },
    ],
    state: 'open',
    locked: false,
    comments: 1,
    created_at: 1589641661000,
    updated_at: 1589750191000,
    closed_at: null,
    author_association: 'NONE',
    url: 'https://github.com/ant-design/ant-design-pro/issues/6599',
    user: 'chenshuai2144',
    avatar:
      'https://gw.alipayobjects.com/zos/antfincdn/XAosXuNZyF/BiazfanxmamNRoxxVxka.png',
  },
  {
    id: 619369062,
    number: 6596,
    title: 'Deno 1.0 发布，有没有取代 node 的优势？',
    labels: [
      {
        name: 'question',
        color: 'success',
      },
    ],
    state: 'open',
    locked: false,
    comments: 2,
    created_at: 1589600520000,
    updated_at: 1589770752000,
    closed_at: null,
    author_association: 'COLLABORATOR',
    url: 'https://github.com/ant-design/ant-design-pro/issues/6596',
    user: 'chenshuai2144',
    avatar:
      'https://gw.alipayobjects.com/zos/antfincdn/XAosXuNZyF/BiazfanxmamNRoxxVxka.png',
  },
  {
    id: 618781702,
    number: 6589,
    title: 'dva 相关问题',
    labels: [
      {
        name: 'question',
        color: 'success',
      },
    ],
    state: 'open',
    locked: false,
    comments: 4,
    created_at: 1589530143000,
    updated_at: 1589775505000,
    closed_at: null,
    author_association: 'NONE',
    url: 'https://github.com/ant-design/ant-design-pro/issues/6589',
    user: 'chenshuai2144',
    avatar:
      'https://gw.alipayobjects.com/zos/antfincdn/XAosXuNZyF/BiazfanxmamNRoxxVxka.png',
  },
  {
    id: 618752606,
    number: 6588,
    title: '创建项目中途报错🐛 [BUG]',
    labels: [
      {
        name: 'bug',
        color: 'error',
      },
    ],
    state: 'open',
    locked: false,
    comments: 0,
    created_at: 1589526859000,
    updated_at: 1589652189000,
    closed_at: null,
    author_association: 'NONE',
    url: 'https://github.com/ant-design/ant-design-pro/issues/6588',
    user: 'chenshuai2144',
    avatar:
      'https://gw.alipayobjects.com/zos/antfincdn/XAosXuNZyF/BiazfanxmamNRoxxVxka.png',
  },
  {
    id: 617300825,
    number: 6576,
    title: '🧐 [问题] Umi UI 资产界面没有antd区块',
    labels: [
      {
        name: 'question',
        color: 'success',
      },
    ],
    state: 'open',
    locked: false,
    comments: 0,
    created_at: 1589362738000,
    updated_at: 1589362738000,
    closed_at: null,
    author_association: 'NONE',
    url: 'https://github.com/ant-design/ant-design-pro/issues/6576',
    user: 'chenshuai2144',
    avatar:
      'https://gw.alipayobjects.com/zos/antfincdn/XAosXuNZyF/BiazfanxmamNRoxxVxka.png',
  },
  {
    id: 617260670,
    number: 6575,
    title: 'qiankun支持👑 [需求]',
    labels: [
      {
        name: 'Feature Request',
        color: 'default',
      },
    ],
    state: 'open',
    locked: false,
    comments: 2,
    created_at: 1589359354000,
    updated_at: 1589652920000,
    closed_at: null,
    author_association: 'NONE',
    url: 'https://github.com/ant-design/ant-design-pro/issues/6575',
    user: 'chenshuai2144',
    avatar:
      'https://gw.alipayobjects.com/zos/antfincdn/XAosXuNZyF/BiazfanxmamNRoxxVxka.png',
  },
]

const testMock = defineMock(
  {
    '/test-pro-table-list': ({ query }) => {
      const page = parseInt(query.page) || 1
      const pageSize = parseInt(query.pageSize) || 10

      // 计算分页数据
      const start = (page - 1) * pageSize
      const end = start + pageSize
      const data = mockTodos.slice(start, end)

      return {
        code: 0,
        data: {
          page,
          pageSize,
          total: mockTodos.length,
          list: data,
          hasNext: end < mockTodos.length,
        },
        msg: '成功',
      }
    },
  },
  true,
)

export default testMock
