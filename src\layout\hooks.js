import {
  getOpenUserInfo,
  getOpenUserMenus,
  getOpenUserRoles,
} from '@/api/common'
import { useUserActions } from '@/store/userStore'
import { isEmpty } from '@/utils/is'
import { useCallback, useEffect, useMemo, useState } from 'react'

// 获取账号信息
export function useAccountInfo() {
  const [data, setData] = useState([])
  const [errorMessage, setErrorMessage] = useState('')
  const { setUserInfo, setUserRoles, setUserMenu } = useUserActions()

  const getInfo = useCallback(() => {
    Promise.all([getOpenUserInfo(), getOpenUserRoles(), getOpenUserMenus()])
      .then((responses) => {
        const [userData, userRoles, userMenu] = responses
        setData(responses)
        setUserInfo(userData)
        setUserRoles(userRoles)
        setUserMenu(userMenu)
      })
      .catch((error) => {
        console.log('Layout 获取用户信息失败', error)
        setErrorMessage('获取用户信息失败')
      })
  }, [setUserInfo, setUserMenu, setUserRoles])

  useEffect(() => {
    getInfo()
  }, [getInfo])

  // 账号数据是否全部拉取完成
  const accountInfo = useMemo(() => {
    const bol = !isEmpty(data) && data.length === 3
    const [userData, userRoles, userMenu] = data
    return bol
      ? {
          userData,
          userRoles,
          userMenu,
        }
      : null
  }, [data])

  return {
    accountInfo,
    errorMessage,
  }
}
