import { Descriptions } from 'antd'

// 支原体DNA染色法检测组件
function MycoplasmaDNAStainingTestComponent({ data }) {
  return (
    <Descriptions
      column={3}
      size="small"
      items={[
        {
          key: 'stainingResult',
          label: '染色结果',
          children: data?.stainingResult || '-',
        },
        {
          key: 'stainingMethod',
          label: '染色方法',
          children: data?.stainingMethod || '-',
        },
        {
          key: 'testMethod',
          label: '检测方法',
          children: data?.testMethod || '-',
        },
      ]}
    />
  )
}

export default MycoplasmaDNAStainingTestComponent
