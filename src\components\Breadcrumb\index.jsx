import { useRouter } from '@/hooks/router'
import { ArrowLeftOutlined, HomeOutlined } from '@ant-design/icons'
import { Breadcrumb as AntBreadcrumb, Space } from 'antd'
import { Children, useMemo } from 'react'
import { Link, useMatches } from 'react-router'

function Breadcrumb({
  // 用于二层页面返回按钮
  showBackArrow,
  children,
}) {
  // 当前匹配项
  const matches = useMatches()
  // 面包屑数据列表
  const BreadcrumbItems = useMemo(() => {
    const hasTitleItems = matches.filter(
      (item) => item.handle && item.handle()?.title,
    )
    const items = hasTitleItems.map((item, index) => {
      const title = item.handle(item.params)?.title || ''
      return {
        href: index === hasTitleItems.length - 1 ? undefined : item.pathname,
        titleText: title,
        title: (
          <Space size={5}>
            {index === 0 && <HomeOutlined />}
            {title}
          </Space>
        ),
      }
    })

    return items
  }, [matches])

  // 最后一项的titleText
  const lastItemTitle = useMemo(() => {
    return matches.length > 1
      ? BreadcrumbItems[BreadcrumbItems.length - 1]?.titleText
      : ''
  }, [matches, BreadcrumbItems])

  function itemRender(currentRoute) {
    const { href, title } = currentRoute

    return href ? (
      <Link
        to={href}
        replace>
        {title}
      </Link>
    ) : (
      <span>{currentRoute.title}</span>
    )
  }

  const router = useRouter()

  return (
    <div className="flex w-full items-center justify-between">
      <Space
        style={{ margin: '12px 0', minHeight: '32px', lineHeight: '32px' }}
        align="center"
        size={14}>
        {matches.length > 1 && (
          <Space
            size={12}
            align="center"
            style={{ cursor: 'pointer' }}
            onClick={() => router.back()}>
            {showBackArrow && <ArrowLeftOutlined />}
            <h1 className="text-[20px] font-medium">{lastItemTitle}</h1>
          </Space>
        )}
        <AntBreadcrumb
          items={BreadcrumbItems}
          itemRender={itemRender}
        />
      </Space>
      {children && (
        <div className="flex items-center space-x-[8px]">
          {Children.map(children, (child) => {
            return child
          })}
        </div>
      )}
    </div>
  )
}

export default Breadcrumb
