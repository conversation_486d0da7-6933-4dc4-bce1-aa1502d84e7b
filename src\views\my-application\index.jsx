import Breadcrumb from '@/components/Breadcrumb'
import Content from '@/layout/components/Content'
import { Tabs } from 'antd'
import { useState } from 'react'
import { useOutlet } from 'react-router'
import OutboundManagement from './OutboundManagement'
import QualityInspection from './QualityInspection'

function MyApplication() {
  const [activeKey, setActiveKey] = useState('1')

  const outlet = useOutlet()

  const handleTabChange = (key) => {
    setActiveKey(key)
  }

  if (outlet) {
    return outlet
  }

  return (
    <>
      <Breadcrumb />
      <Content>
        <Tabs
          activeKey={activeKey}
          onChange={handleTabChange}
          items={[
            {
              label: '质检',
              key: '1',
              children: <QualityInspection />,
            },
            {
              label: '出库',
              key: '2',
              children: <OutboundManagement />,
            },
          ]}
        />
      </Content>
    </>
  )
}

export default MyApplication
