import { Descriptions } from 'antd'

// 支原体培养法检测组件
function MycoplasmaCultureTestComponent({ data }) {
  return (
    <Descriptions
      column={3}
      size="small"
      items={[
        {
          key: 'cultureResult',
          label: '培养结果',
          children: data?.cultureResult || '-',
        },
        {
          key: 'cultureConditions',
          label: '培养条件',
          children: data?.cultureConditions || '-',
        },
        {
          key: 'testMethod',
          label: '检测方法',
          children: data?.testMethod || '-',
        },
      ]}
    />
  )
}

export default MycoplasmaCultureTestComponent
