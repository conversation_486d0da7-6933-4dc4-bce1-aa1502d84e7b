import { Descriptions } from 'antd'

// PCR种属鉴定组件
function PCRSpeciesTestComponent({ data }) {
  return (
    <Descriptions
      column={3}
      size="small"
      items={[
        {
          key: 'species',
          label: '种属',
          children: data?.species || '-',
        },
        {
          key: 'identificationResult',
          label: '鉴定结果',
          children: data?.identificationResult || '-',
        },
        {
          key: 'testMethod',
          label: '检测方法',
          children: data?.testMethod || '-',
        },
      ]}
    />
  )
}

export default PCRSpeciesTestComponent
