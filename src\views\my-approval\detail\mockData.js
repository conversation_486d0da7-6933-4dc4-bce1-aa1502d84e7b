// 质检审批详情页面的mock数据
export const mockDetailData = {
  cellInfo: {
    cellCenterNumber: 'CC20240001',
    cellChineseName: '人胚肾细胞',
    cellEnglishName: 'HEK293T',
    cellSpecies: '人',
    cellSource: '胚胎肾脏',
    originalBatchNumber: 'OB20240115001',
    frozenBatchNumber: 'FB20240115001',
    cellType: '贴壁细胞',
    frozenTubeCount: 20,
    resuscitationTubeCount: 3,
    stockTubeCount: 17,
    cellGeneration: 'P8',
    cultureMedium: 'DMEM + 10% FBS + 1% P/S',
    cultureConditions: '37°C, 5% CO2, 湿度95%',
    receiveResuscitationTime: '2024-01-15 10:30:00',
    resuscitationCellViability: '96.5%',
    cellLibraryLevel: '工作库',
    remark: '细胞状态良好，生长活跃，形态正常',
  },
  inboundInfo: {
    inboundCategory: '工作库入库',
    inboundEquipment: '液氮罐A1-MVE-815P',
    inboundTubeCount: 20,
    inboundTime: '2024-01-15 14:30:00',
    inboundPerson: '张三',
    positionList: [
      {
        liquidNitrogenTank: 'A1',
        basket: 'B1',
        layerNumber: 1,
        holePosition: 'A1-A10',
        tubeCount: 10,
      },
      {
        liquidNitrogenTank: 'A1',
        basket: 'B1',
        layerNumber: 2,
        holePosition: 'A1-A10',
        tubeCount: 10,
      },
    ],
  },
  outboundInfo: {
    outboundList: [
      {
        id: 'OUT20240120001',
        outboundTime: '2024-01-20 09:15:00',
        outboundTubeCount: 2,
        cellLibraryLevel: '工作库',
        outboundPerson: '李四',
        outboundPurpose: '细胞转染实验',
        outboundIdentifier: 'EXP-TF-001',
      },
      {
        id: 'OUT20240122001',
        outboundTime: '2024-01-22 14:20:00',
        outboundTubeCount: 1,
        cellLibraryLevel: '工作库',
        outboundPerson: '王五',
        outboundPurpose: '药物筛选实验',
        outboundIdentifier: 'EXP-DS-002',
      },
    ],
  },
  qualityInfo: {
    testItems: [
      {
        id: 1,
        testItem: '1',
        testDate: '2024-01-15',
        testResult: '合格',
        testPerson: '张三',
        remarks: 'STR图谱匹配正确',
        detailData: {
          strProfile: 'D3S1358: 15,16; vWA: 17,18; FGA: 21,24',
          matchResult: '100%匹配',
          testMethod: 'STR-PCR扩增+毛细管电泳',
        },
      },
      {
        id: 2,
        testItem: '2',
        testDate: '2024-01-16',
        testResult: '合格',
        testPerson: '李四',
        remarks: '同工酶谱正常',
        detailData: {
          enzymePattern: 'LDH: A4B0; MDH: 1-1; G6PD: B',
          testResult: '符合人源细胞特征',
          testMethod: '聚丙烯酰胺凝胶电泳',
        },
      },
      {
        id: 3,
        testItem: '3',
        testDate: '2024-01-17',
        testResult: '合格',
        testPerson: '王五',
        remarks: '种属鉴定正确',
        detailData: {
          species: 'Homo sapiens',
          identificationResult: '人源细胞',
          testMethod: 'COI基因PCR扩增+测序',
        },
      },
      {
        id: 4,
        testItem: '4',
        testDate: '2024-01-18',
        testResult: '合格',
        testPerson: '赵六',
        remarks: 'DNA条形码鉴定正确',
        detailData: {
          barcodeSequence: 'ATCGATCGATCGATCG...',
          similarity: '99.8%',
          testMethod: 'DNA条形码测序技术',
        },
      },
      {
        id: 5,
        testItem: '5',
        testDate: '2024-01-19',
        testResult: '合格',
        testPerson: '钱七',
        remarks: '支原体培养阴性',
        detailData: {
          cultureResult: '阴性',
          cultureConditions: '37℃，5% CO2，PPLO培养基，14天',
          testMethod: '支原体专用培养基培养法',
        },
      },
      {
        id: 6,
        testItem: '6',
        testDate: '2024-01-20',
        testResult: '合格',
        testPerson: '孙八',
        remarks: '支原体PCR检测阴性',
        detailData: {
          pcrResult: '阴性',
          targetGene: '16S rRNA基因',
          testMethod: '实时荧光定量PCR',
        },
      },
      {
        id: 7,
        testItem: '7',
        testDate: '2024-01-21',
        testResult: '合格',
        testPerson: '周九',
        remarks: '支原体DNA染色阴性',
        detailData: {
          stainingResult: '阴性',
          stainingMethod: 'DAPI染色法',
          testMethod: '荧光显微镜观察',
        },
      },
      {
        id: 8,
        testItem: '8',
        testDate: '2024-01-22',
        testResult: '合格',
        testPerson: '吴十',
        remarks: '细菌真菌接种阴性',
        detailData: {
          bacteriaResult: '阴性',
          fungiResult: '阴性',
          inoculationMethod: '平板接种法',
        },
      },
    ],
    qualityHistory: [
      {
        id: 'TASK001',
        status: '已完成',
        completionTime: '2024-01-16 16:30:00',
        tester: '赵六',
        testItems: [
          {
            id: 'ITEM001',
            testItem: '细胞活力检测',
            testResult: '合格',
            testTime: '2024-01-16 15:30:00',
            tester: '赵六',
            detailData: {
              viability: '96.5',
              totalCells: '2500000',
              liveCells: '2412500',
              deadCells: '87500',
              testMethod: '台盼蓝染色法',
              remarks: '细胞活力优秀，形态正常，无明显死细胞聚集',
            },
          },
          {
            id: 'ITEM002',
            testItem: '微生物检测',
            testResult: '合格',
            testTime: '2024-01-16 14:00:00',
            tester: '赵六',
            detailData: {
              bacteriaResult: '阴性',
              fungusResult: '阴性',
              mycoplasmaResult: '阴性',
              testMethod: '培养法 + PCR检测',
              cultureConditions: '37°C, 48小时培养',
              remarks: '无细菌、真菌及支原体污染，培养基清澈透明',
            },
          },
          {
            id: 'ITEM004',
            testItem: '细胞鉴定',
            testResult: '合格',
            testTime: '2024-01-16 13:00:00',
            tester: '赵六',
            detailData: {
              identificationMethod: 'STR基因型检测',
              markerExpression: 'CD44+, CD90+, CD105+',
              morphology: '典型上皮细胞形态，贴壁生长',
              growthCharacteristics: '生长迅速，倍增时间约24小时',
              identificationResult: '确认为HEK293T细胞系',
              remarks: '基因型与标准库匹配度99.8%，细胞纯度高',
            },
          },
        ],
      },
      {
        id: 'TASK002',
        status: '任务进行中',
        testItems: [
          {
            id: 'ITEM003',
            testItem: '病毒检测',
            testResult: '检测中',
            testTime: '2024-01-17 10:00:00',
            tester: '孙七',
            detailData: {
              virusType: '常见病毒谱检测',
              detectionResult: '检测进行中',
              testMethod: 'qRT-PCR法',
              sensitivity: '检测限度: 10 copies/ml',
              remarks: '已完成样本处理，正在进行PCR扩增检测',
            },
          },
        ],
      },
      {
        id: 'TASK003',
        status: '已完成',
        completionTime: '2024-01-15 17:00:00',
        tester: '周八',
        testItems: [
          {
            id: 'ITEM005',
            testItem: '细胞活力检测',
            testResult: '合格',
            testTime: '2024-01-15 16:30:00',
            tester: '周八',
            detailData: {
              viability: '95.2',
              totalCells: '1800000',
              liveCells: '1713600',
              deadCells: '86400',
              testMethod: '台盼蓝染色法',
              remarks: '复苏后细胞活力检测，状态良好',
            },
          },
        ],
      },
    ],
  },
}

// 导出函数，模拟API调用
export const getMockDetailData = () => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        data: mockDetailData,
        code: 200,
        message: 'success',
      })
    }, 500) // 模拟网络延迟
  })
}
