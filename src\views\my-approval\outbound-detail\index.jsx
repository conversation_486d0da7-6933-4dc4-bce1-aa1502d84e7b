import {
  approveApply,
  getCellApplyInfoDetail,
  rejectApply,
} from '@/api/myApproval'
import Breadcrumb from '@/components/Breadcrumb'
import Content, { ContentTitle } from '@/layout/components/Content'
import { Button, Descriptions, Input, Modal, Spin, Table, message } from 'antd'
import { useCallback, useEffect, useState } from 'react'
import { useNavigate, useParams } from 'react-router'

const { TextArea } = Input

const OutboundDetail = () => {
  const { id } = useParams()
  const navigate = useNavigate()
  const [detail, setDetail] = useState(null)
  const [loading, setLoading] = useState(true)
  const [actionLoading, setActionLoading] = useState(false)
  const [rejectModalVisible, setRejectModalVisible] = useState(false)
  const [rejectReason, setRejectReason] = useState('')

  const fetchDetail = useCallback(async () => {
    setLoading(true)
    try {
      const res = await getCellApplyInfoDetail(id)
      setDetail(res)
    } catch {
      message.error('获取详情失败')
      navigate(-1)
    } finally {
      setLoading(false)
    }
  }, [id, navigate])

  useEffect(() => {
    fetchDetail()
  }, [fetchDetail])

  const handleApprove = useCallback(async () => {
    setActionLoading(true)
    try {
      await approveApply(id)
      message.success('审核通过成功')
      navigate(-1)
    } catch (error) {
      message.error(`操作失败: ${error.message}`)
    } finally {
      setActionLoading(false)
    }
  }, [id, navigate])

  const handleReject = useCallback(() => {
    setRejectReason('')
    setRejectModalVisible(true)
  }, [])

  const confirmReject = useCallback(async () => {
    if (!rejectReason.trim()) {
      message.warning('请填写驳回原因')
      return
    }
    setActionLoading(true)
    try {
      await rejectApply(id, rejectReason.trim())
      message.success('驳回成功')
      setRejectModalVisible(false)
      navigate(-1)
    } catch (error) {
      message.error(`操作失败: ${error.message}`)
    } finally {
      setActionLoading(false)
    }
  }, [id, navigate, rejectReason])

  // 细胞信息数据
  const cellInfoItems = [
    {
      key: 'cellCode',
      label: '细胞中心编号',
      children: detail?.cellCode,
    },
    {
      key: 'cellNameCn',
      label: '细胞中文名称',
      children: detail?.cellNameCn,
    },
    {
      key: 'cellNameEn',
      label: '细胞英文名称',
      children: detail?.cellNameEn,
    },
    {
      key: 'cellSpecies',
      label: '细胞种属',
      children: detail?.cellSpecies === 1 ? '人' : '小鼠',
    },
    {
      key: 'sourceOrgan',
      label: '细胞来源',
      children: detail?.sourceOrgan,
    },
    {
      key: 'originalBatchNo',
      label: '原始批次号',
      children: detail?.originalBatchNo || '-',
    },
    {
      key: 'frozenBatchNo',
      label: '冻存批次号',
      children: detail?.frozenBatchNo || '-',
    },
    {
      key: 'cellType',
      label: '细胞类型',
      children: detail?.cellType,
    },
    {
      key: 'frozenTubeCount',
      label: '冻存管数',
      children: detail?.frozenTubeCount || '-',
    },
    {
      key: 'recoveryTubeCount',
      label: '复苏管数',
      children: detail?.recoveryTubeCount || '-',
    },
    {
      key: 'stockTubeCount',
      label: '库存管数',
      children: detail?.stockTubeCount || '-',
    },
    {
      key: 'cellGeneration',
      label: '细胞代次',
      children: detail?.cellGeneration,
    },
    {
      key: 'medium',
      label: '培养基',
      children: detail?.medium,
    },
    {
      key: 'cultureCondition',
      label: '培养条件',
      children: detail?.cultureCondition,
    },
    {
      key: 'acceptRecoveryTime',
      label: '接收/复苏时间',
      children: detail?.acceptRecoveryTime,
    },
    {
      key: 'storageLevel',
      label: '细胞库级别',
      children: detail?.storageLevel || '-',
    },
    {
      key: 'remarks',
      label: '备注',
      children: detail?.remarks || '-',
      span: 2,
    },
  ]

  // 出库信息数据
  const outboundInfoItems = [
    {
      key: 'outboundCount',
      label: '出库管数',
      children: detail?.outboundCount || '-',
    },
    {
      key: 'outboundTime',
      label: '出库时间',
      children: detail?.outboundTime || '-',
    },
    {
      key: 'outboundPerson',
      label: '出库人',
      children: detail?.outboundPerson || '-',
    },
    {
      key: 'outboundPurpose',
      label: '出库目的',
      children: detail?.outboundPurpose || '-',
    },
  ]

  // 细胞位置表格列配置
  const positionColumns = [
    {
      title: '细胞中文名称',
      dataIndex: 'cellNameCn',
      key: 'cellNameCn',
    },
    {
      title: '细胞英文名称',
      dataIndex: 'cellNameEn',
      key: 'cellNameEn',
    },
    {
      title: '库类别',
      dataIndex: 'storageType',
      key: 'storageType',
    },
    {
      title: '液氮罐',
      dataIndex: 'nitrogenTank',
      key: 'nitrogenTank',
    },
    {
      title: '提篮',
      dataIndex: 'basket',
      key: 'basket',
    },
    {
      title: '层数',
      dataIndex: 'layer',
      key: 'layer',
    },
    {
      title: '孔位',
      dataIndex: 'position',
      key: 'position',
    },
    {
      title: '管数',
      dataIndex: 'tubeCount',
      key: 'tubeCount',
    },
    {
      title: '操作',
      key: 'action',
      render: () => <a onClick={() => message.info('查看功能待实现')}>查看</a>,
    },
  ]

  // 模拟细胞位置数据
  const positionData = [
    {
      key: '1',
      cellNameCn: detail?.cellNameCn || '',
      cellNameEn: detail?.cellNameEn || '',
      storageType: '液氮库',
      nitrogenTank: 'LN-001',
      basket: 'B-01',
      layer: '3',
      position: 'A1',
      tubeCount: 5,
    },
  ]

  return (
    <div className="flex h-full flex-col overflow-hidden">
      <Breadcrumb showBackArrow>
        <Button
          type="primary"
          onClick={handleApprove}
          loading={actionLoading}
          className="mr-2">
          审核通过
        </Button>
        <Button
          onClick={handleReject}
          loading={actionLoading}>
          驳回
        </Button>
      </Breadcrumb>

      <div className="flex-1 overflow-auto bg-white">
        <Content>
          <Spin spinning={loading}>
            {detail && (
              <>
                <ContentTitle title="细胞信息" />
                <Descriptions
                  items={cellInfoItems}
                  column={3}
                  bordered
                  size="small"
                  className="!mb-8"
                />

                <ContentTitle title="出库信息" />
                <Descriptions
                  items={outboundInfoItems}
                  column={3}
                  bordered
                  size="small"
                  className="!mb-6"
                />

                <div className="mb-4">
                  <h3 className="text-base font-medium">细胞位置</h3>
                </div>

                <Table
                  columns={positionColumns}
                  dataSource={positionData}
                  pagination={false}
                  size="small"
                  bordered
                />
              </>
            )}
          </Spin>
        </Content>
      </div>

      {/* 驳回原因弹窗 */}
      <Modal
        title="驳回原因"
        open={rejectModalVisible}
        onOk={confirmReject}
        onCancel={() => setRejectModalVisible(false)}
        okText="确认"
        cancelText="取消"
        confirmLoading={actionLoading}
        width={500}>
        <div className="mb-4">
          <p>请填写驳回原因：</p>
        </div>
        <TextArea
          rows={4}
          value={rejectReason}
          onChange={(e) => setRejectReason(e.target.value)}
          placeholder="请输入驳回原因"
          maxLength={500}
          showCount
        />
      </Modal>
    </div>
  )
}

export default OutboundDetail
