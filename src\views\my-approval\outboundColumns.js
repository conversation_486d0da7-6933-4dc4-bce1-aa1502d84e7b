// 出库审批模块的列配置

// 出库审批数据列配置
export const getOutboundColumns = () => [
  {
    dataIndex: 'index',
    valueType: 'indexBorder',
    width: 48,
    title: '序号',
  },
  {
    title: '出库订单号',
    dataIndex: 'outboundNo',
    width: 150,
  },
  {
    title: '入库订单号',
    dataIndex: 'stockInNo',
    width: 150,
  },
  {
    title: '细胞中心编号',
    dataIndex: 'cellCode',
    width: 150,
  },
  {
    title: '细胞名称',
    dataIndex: 'cellName',
    width: 150,
    ellipsis: true,
    render: (_, record) => {
      return record.cellNameCn || record.cellNameEn
    },
    hideInTable: true,
  },
  {
    title: '细胞中文名称',
    dataIndex: 'cellNameCn',
    width: 150,
    ellipsis: true,
    hideInSearch: true,
  },
  {
    title: '细胞英文名称',
    dataIndex: 'cellNameEn',
    width: 150,
    ellipsis: true,
    hideInSearch: true,
  },
  {
    title: '细胞来源',
    dataIndex: 'sourceOrgan',
    width: 120,
  },
  {
    title: '细胞库级别',
    dataIndex: 'storageLibraryLevel',
    width: 120,
    valueType: 'select',
    valueEnum: {
      一级库: { text: '一级库' },
      二级库: { text: '二级库' },
      三级库: { text: '三级库' },
    },
  },
  {
    title: '检测项目',
    dataIndex: 'analysisItemText',
    width: 150,
    ellipsis: true,
    hideInSearch: true,
  },
  {
    title: '原始批次号',
    dataIndex: 'originalBatchNo',
    width: 150,
  },
  {
    title: '冻存批次号',
    dataIndex: 'cryoBatchNo',
    width: 150,
  },
  {
    title: '出库管数',
    dataIndex: 'outboundNum',
    width: 100,
    hideInSearch: true,
  },
  {
    title: '出库人',
    dataIndex: 'outboundUser',
    width: 120,
    hideInSearch: true,
  },
  {
    title: '出库目的',
    dataIndex: 'outboundPurpose',
    width: 150,
    ellipsis: true,
    hideInSearch: true,
  },
  {
    title: '出库标识',
    dataIndex: 'outboundFlag',
    width: 120,
    hideInSearch: true,
  },
  {
    title: '冻存管数',
    dataIndex: 'frozenNum',
    width: 100,
    hideInSearch: true,
  },
  {
    title: '复苏管数',
    dataIndex: 'recoveryNum',
    width: 100,
    hideInSearch: true,
  },
  {
    title: '库存管数',
    dataIndex: 'currentNum',
    width: 100,
    hideInSearch: true,
  },
  {
    title: '细胞代次',
    dataIndex: 'cellGeneration',
    width: 100,
    hideInSearch: true,
  },
  {
    title: '细胞类型',
    dataIndex: 'cellType',
    width: 120,
    hideInSearch: true,
  },
  {
    title: '培养基',
    dataIndex: 'medium',
    width: 120,
    ellipsis: true,
    hideInSearch: true,
  },
  {
    title: '培养条件',
    dataIndex: 'cultureCondition',
    width: 120,
    ellipsis: true,
    hideInSearch: true,
  },
  {
    title: '接收/复苏时间',
    dataIndex: 'recoveryTime',
    width: 150,
    valueType: 'dateTime',
    hideInSearch: true,
  },
  {
    title: '备注',
    dataIndex: 'remarks',
    width: 150,
    ellipsis: true,
    hideInSearch: true,
  },
  {
    title: '入库管数',
    dataIndex: 'tubeNum',
    width: 100,
    hideInSearch: true,
  },
  {
    title: '入库时间',
    dataIndex: 'stockInTime',
    width: 150,
    valueType: 'dateTime',
    hideInSearch: true,
  },
  {
    title: '入库人',
    dataIndex: 'stockInUser',
    width: 120,
    hideInSearch: true,
  },
  {
    title: '质检历史',
    dataIndex: 'checkHistory',
    width: 120,
    ellipsis: true,
    hideInSearch: true,
  },
  {
    title: 'STR',
    dataIndex: 'strFlag',
    width: 100,
    hideInSearch: true,
  },
  {
    title: 'STR备注',
    dataIndex: 'strRemark',
    width: 120,
    ellipsis: true,
    hideInSearch: true,
  },
  {
    title: '支原体PCR',
    dataIndex: 'mycoPcr',
    width: 120,
    hideInSearch: true,
  },
  {
    title: '支原体培养',
    dataIndex: 'mycoCulture',
    width: 120,
    hideInSearch: true,
  },
  {
    title: '支原体DNA',
    dataIndex: 'mycoDna',
    width: 120,
    hideInSearch: true,
  },
  {
    title: '种属鉴定PCR',
    dataIndex: 'speciesPcr',
    width: 150,
    hideInSearch: true,
  },
  {
    title: '细菌真菌25℃',
    dataIndex: 'fungusLowTemp',
    width: 120,
    hideInSearch: true,
  },
  {
    title: '细菌真菌35℃',
    dataIndex: 'fungusHighTemp',
    width: 120,
    hideInSearch: true,
  },
  {
    title: 'DNA Barcoding',
    dataIndex: 'dnaBarcoding',
    width: 150,
    hideInSearch: true,
  },
  {
    title: '质检时间',
    dataIndex: 'checkTime',
    width: 250,
    valueType: 'dateTime',
    hideInSearch: true,
  },
  {
    title: '申请时间',
    dataIndex: 'createdAt',
    width: 150,
    valueType: 'dateTimeRange',
    hideInTable: true,
    search: {
      transform: (value) => {
        return {
          createdAtStart: value?.[0],
          createdAtEnd: value?.[1],
        }
      },
    },
  },
  {
    title: '申请时间',
    dataIndex: 'createdAt',
    width: 250,
    valueType: 'dateTime',
    hideInSearch: true,
  },
]
