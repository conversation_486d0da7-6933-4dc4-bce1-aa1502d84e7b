import { useRouter } from '@/hooks/router'
import { useUserMenu } from '@/store/userStore'
import { isValidMenuItem } from '@/utils/menu'
import TreeUtils from '@/utils/tree'
import { Layout as AntdLayout, ConfigProvider, Menu, theme } from 'antd'
import { useMemo, useState } from 'react'
import { Outlet, useMatches } from 'react-router'
import LogoFont from './components/LogoFont'
import RightAction from './components/RightAction'

const { Header, Sider: AntdSider } = AntdLayout

function Sider({ colorBgContainer }) {
  const menuTree = useUserMenu()

  const siderMenu = useMemo(() => {
    // 格式化菜单
    const formatMenuTree = (menuTree) => {
      if (!Array.isArray(menuTree)) {
        menuTree = [menuTree]
      }

      return menuTree.filter(isValidMenuItem).map((menuItem) => {
        const formattedMenu = {
          id: menuItem.id,
          key: menuItem.path || menuItem.id,
          label: menuItem.name,
          icon: <i className={menuItem.meta?.icon + ' iconfont' || ''} />,
        }

        if (menuItem.children && menuItem.children.length > 0) {
          formattedMenu.children = formatMenuTree(menuItem.children)
        }

        return formattedMenu
      })
    }

    return formatMenuTree(menuTree)
  }, [menuTree])

  const { push } = useRouter()
  const matches = useMatches()

  const matchPathnames = useMemo(() => {
    return matches.map((match) => match.pathname)
  }, [matches])

  // 当前选中高亮的菜单
  const defaultSelectedKeys = useMemo(() => {
    const keys = []
    for (const match of matches) {
      const { pathname, handle } = match
      let active = ''
      if (handle) {
        const { activeMenu } = handle()
        if (activeMenu) {
          active = activeMenu
        }
      }

      // 对于不在菜单里的嵌套路由，例如从列表点新增页面，点开详情页，
      // 详情页的菜单项是空的，所以需要手动指定一个高亮菜单项
      if (active) {
        keys.push(active)
      } else {
        keys.push(pathname)
      }
    }
    return keys
  }, [matches])

  // 展开的菜单，默认展开选中
  const defaultOpenKeys = useMemo(() => {
    const arr = TreeUtils.filterTree(menuTree, (node) =>
      matchPathnames.includes(node.path),
    )
    // 不打平只能展开第一级，打平可以展开所有
    return TreeUtils.flatten(arr).map((node) => node.path || node.id)
  }, [matchPathnames, menuTree])

  const [collapsed, setCollapsed] = useState(false)
  return (
    <AntdSider
      collapsible
      collapsed={collapsed}
      breakpoint="xl"
      onCollapse={(value) => setCollapsed(value)}
      width={200}
      style={{
        overflow: 'auto',
        scrollbarWidth: 'thin',
        background: colorBgContainer,
        paddingTop: 24,
      }}>
      <Menu
        style={{ height: '100%', borderRight: 0 }}
        items={siderMenu}
        selectedKeys={defaultSelectedKeys}
        defaultOpenKeys={defaultOpenKeys}
        defaultSelectedKeys={defaultSelectedKeys}
        mode="inline"
        onClick={(e) => {
          console.log('e', e)
          push(e.key)
        }}
      />
    </AntdSider>
  )
}

function Layout() {
  const {
    token: { colorBgContainer },
  } = theme.useToken()

  return (
    <ConfigProvider
      theme={{
        components: {
          Layout: {
            headerPadding: '0 20px',
            height: 60,
            headerBg: 'linear-gradient(92deg, #3975c6 0%, #3c73e1 100%)',
            triggerHeight: 32,
            triggerBg: '#e6f4ff',
          },
        },
      }}>
      <AntdLayout
        style={{
          height: '100vh',
        }}>
        <Header className="flex items-center justify-between">
          <LogoFont />
          <RightAction />
        </Header>
        <AntdLayout>
          <Sider colorBgContainer={colorBgContainer} />
          <AntdLayout style={{ padding: '0 20px 20px' }}>
            <Outlet />
          </AntdLayout>
        </AntdLayout>
      </AntdLayout>
    </ConfigProvider>
  )
}

export default Layout
