/**
 * 格式化数字为千分位字符串的 React 组件。
 *
 * @param {object} props 组件的属性。
 * @param {number|string} props.value 要格式化的数字值。
 * @param {number} [props.decimals=0] 保留的小数位数，默认为 0。
 * @param {string} [props.prefix=''] 数字前缀，例如货币符号 '$'。默认为空字符串。
 * @param {string} [props.suffix=''] 数字后缀，例如单位 'kg'。默认为空字符串。
 * @param {string} [props.className=''] 用于设置组件的 CSS 类名。
 * @param {object} [props.style={}] 用于设置组件的内联样式（一个 JavaScript 对象）。
 * @returns {JSX.Element|null} 格式化后的千分位字符串，如果 value 不存在则返回 null。
 */
function ThousandSeparator(props) {
  const {
    value,
    decimals = 0,
    prefix = '',
    suffix = '',
    className = '',
    style = {},
  } = props

  const formatNumber = (num) => {
    if (num === null || num === undefined) {
      return ''
    }
    const numStr = Number(num).toFixed(decimals)
    const parts = numStr.split('.')
    parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',')
    return prefix + parts.join('.') + suffix
  }

  if (value === null || value === undefined) {
    return null // 不渲染任何内容
  }

  const formattedValue = formatNumber(value)

  return (
    <span
      className={className}
      style={style}>
      {formattedValue}
    </span>
  )
}

export default ThousandSeparator
