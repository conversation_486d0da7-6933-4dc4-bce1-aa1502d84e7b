import js from '@eslint/js'
import eslintConfigPrettier from 'eslint-config-prettier'
import react from 'eslint-plugin-react'
import reactHooks from 'eslint-plugin-react-hooks'
import reactRefresh from 'eslint-plugin-react-refresh'
import { defineConfig } from 'eslint/config'
import globals from 'globals'

/**
 * "off" or 0 - turn the rule off.
    "off" 或 0 - 关闭规则。
    "warn" or 1 - turn the rule on as a warning (doesn’t affect exit code).
    "warn" 或 1 - 将规则作为警告开启（不影响退出代码）。
    "error" or 2 - turn the rule on as an error (exit code is 1 when triggered).
    "error" 或 2 - 将规则作为错误开启（触发时退出代码为 1）。
 */
export default defineConfig([
  {
    files: ['**/*.{js,jsx}'],
    ignores: [
      'node_modules',
      'dist',
      'package-lock.json',
      'pnpm-lock.yaml',
      'package.json',
      'docs',
      'dist',
    ],
    // 指定react版本来交给 eslint-plugin-react 使用 不然会存在警告
    settings: {
      react: {
        // 自动检查
        version: 'detect',
      },
    },
    extends: [
      js.configs.recommended,
      react.configs.flat.recommended,
      react.configs.flat['jsx-runtime'],
      reactHooks.configs['recommended-latest'],
      reactRefresh.configs.vite,
    ],
    languageOptions: {
      globals: globals.browser,
      parserOptions: {
        ecmaVersion: 'latest',
        ecmaFeatures: { jsx: true },
        sourceType: 'module',
      },
    },
    rules: {
      'react/jsx-no-target-blank': 'off',
      'react-refresh/only-export-components': [
        'warn',
        { allowConstantExport: true },
      ],
      'prefer-const': 2,
      'no-const-assign': 2,
      'no-undef': 'error',
      'no-unused-vars': 'error',
      'react-hooks/rules-of-hooks': 'error',
      'react-hooks/exhaustive-deps': 'error',
      'react/jsx-closing-tag-location': 'error',
      'react/self-closing-comp': [
        2,
        {
          component: true, // 要求对组件使用自闭合标签
          html: true, // 不要求对 HTML 标记使用自闭合标签
        },
      ],
      'react/prop-types': 'off',
    },
  },
  // https://github.com/prettier/eslint-config-prettier
  eslintConfigPrettier,
])
