/**
 * 把数字格式化为千分位
 * @param {Number} num 需要格式化的数字
 * @param {Number} precision 小数点后保留的位数
 * @returns
 */
export function numberKilobit(num, precision = 0) {
  const ZERO = 0
  const number = +num
  const finalP =
    typeof precision === 'number'
      ? precision
      : number.toString().split('.')[1]?.length || 0
  if (typeof number === 'number' && !isNaN(number)) {
    const regexp = finalP ? /(\d)(?=(\d{3})+\.)/g : /(\d)(?=(\d{3})+$)/g
    return number.toFixed(finalP).replace(regexp, '$1,')
  }
  return ZERO.toFixed(finalP)
}

/**
 * 计算百分比并保留指定小数位数
 * @param {number} numerator 分子
 * @param {number} denominator 分母
 * @param {number} [decimalPlaces=2] 要保留的小数位数，默认为2
 * @returns {string} 百分比字符串
 */
export function calculatePercentage(numerator, denominator, decimalPlaces = 2) {
  // 参数验证
  if (denominator === 0) {
    return 0
  }

  if (isNaN(numerator) || isNaN(denominator)) {
    return NaN
  }

  // 计算百分比
  const percentage = (numerator / denominator) * 100

  // 四舍五入到指定小数位
  const rounded = percentage.toFixed(decimalPlaces)

  // 添加百分号
  return rounded
}
