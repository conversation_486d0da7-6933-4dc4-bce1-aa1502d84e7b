import { Descriptions } from 'antd'
import STRTestComponent from './STRTestComponent'
import IsoenzymeTestComponent from './IsoenzymeTestComponent'
import PCRSpeciesTestComponent from './PCRSpeciesTestComponent'
import DNABarcodingTestComponent from './DNABarcodingTestComponent'
import MycoplasmaCultureTestComponent from './MycoplasmaCultureTestComponent'
import MycoplasmaPCRTestComponent from './MycoplasmaPCRTestComponent'
import MycoplasmaDNAStainingTestComponent from './MycoplasmaDNAStainingTestComponent'
import BacteriaFungiInoculationTestComponent from './BacteriaFungiInoculationTestComponent'

// 质检测试卡片组件
// 根据不同的检测项目展示不同的内容
function QualityTestCard({ testItem, data }) {
  // 根据检测项目类型渲染不同的内容
  const renderContent = () => {
    if (!data) {
      return <div>暂无详细数据</div>
    }

    // 根据检测项目枚举值展示不同的组件
    switch (testItem) {
      case '1':
      case 1:
        return <STRTestComponent data={data} />

      case '2':
      case 2:
        return <IsoenzymeTestComponent data={data} />

      case '3':
      case 3:
        return <PCRSpeciesTestComponent data={data} />

      case '4':
      case 4:
        return <DNABarcodingTestComponent data={data} />

      case '5':
      case 5:
        return <MycoplasmaCultureTestComponent data={data} />

      case '6':
      case 6:
        return <MycoplasmaPCRTestComponent data={data} />

      case '7':
      case 7:
        return <MycoplasmaDNAStainingTestComponent data={data} />

      case '8':
      case 8:
        return <BacteriaFungiInoculationTestComponent data={data} />

      default: {
        // 通用格式，展示所有可用字段
        const items = Object.keys(data).map((key) => ({
          key,
          label: key,
          children: data[key] || '-',
        }))

        return (
          <Descriptions
            column={3}
            size="small"
            items={items}
          />
        )
      }
    }
  }

  return (
    <div>
      <div style={{ marginBottom: 12, color: '#666' }}>
        检测项目详细信息：{testItem}
      </div>
      {renderContent()}
    </div>
  )
}

export default QualityTestCard
