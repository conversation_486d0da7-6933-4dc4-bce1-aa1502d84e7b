import { useUserPermissions, useUserRoles } from '@/store/userStore'
import { authPermission, authRole } from '@/utils/auth'
import { match } from 'path-to-regexp'
import { useMemo } from 'react'
import { createBrowserRouter } from 'react-router'
import { routes, whiteRoutes } from './routes'

/**
 * 判断当前路由是否为白名单路由
 * @returns
 */
export function useWhiteRoute() {
  const pathname = location.pathname
  const isWhiteRoute = whiteRoutes.some((pattern) => {
    return match(pattern)(pathname)
  })

  return isWhiteRoute
}

/**
 * 动态路由 Hook - 根据用户权限过滤路由
 * @param {Array} routes - 需要过滤的路由配置
 * @returns {Array} - 过滤后的路由配置
 */
function useDynamicRoutes(routes) {
  const userPermissions = useUserPermissions()
  const userRoles = useUserRoles()

  return useMemo(() => {
    function filterRoutes(routesToFilter) {
      const res = []
      for (let i = 0; i < routesToFilter.length; i++) {
        const route = routesToFilter[i]

        const { permissions, roles } = route?.handle ? route.handle() : {}

        // 校验当前路由权限
        const hasPermission =
          (!permissions ||
            permissions.some((permission) =>
              authPermission(userPermissions, permission),
            )) &&
          (!roles || roles.some((role) => authRole(userRoles, role)))

        if (hasPermission) {
          // 递归处理子路由
          if (route.children && route.children.length) {
            const filteredChildren = filterRoutes(route.children)
            // 如果子路由全部被过滤，则可能也过滤父路由（可选）
            if (filteredChildren.length === 0) {
              continue // 跳过无有效子路由的父路由
            }
            route.children = filteredChildren
          }
          res.push(route)
        }
      }
      return res
    }

    return filterRoutes(routes)
  }, [userPermissions, userRoles, routes])
}

/**
 * 根据权限动态生成路由器
 */
export function useBrowserRouter() {
  const asyncRoutes = useDynamicRoutes(routes)

  return useMemo(() => {
    // 创建路由
    return createBrowserRouter(asyncRoutes, {
      basename: import.meta.env.BASE_URL, // 设置公共路径
    })
  }, [asyncRoutes])
}
