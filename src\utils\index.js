/**
 * 传入length 根据字符串生产length 长度的Id
 */
export const getUid = (() => {
  const heyStack = '0123456789abcdefghijklmnopqrstuvwxyz'
  const randomInt = () =>
    Math.floor(Math.random() * Math.floor(heyStack.length))

  const id = (length = 24) =>
    Array.from({ length }, () => heyStack[randomInt()]).join('')
  // 因为选择器除开特殊符号,第一个字符必须是字母
  return id
})()

export function deepClone(obj) {
  if (obj === null || typeof obj !== 'object') {
    return obj
  }

  if (Array.isArray(obj)) {
    return obj.map((item) => deepClone(item))
  }

  const cloned = {}
  for (const key in obj) {
    if (Object.prototype.hasOwnProperty.call(obj, key)) {
      cloned[key] = deepClone(obj[key])
    }
  }
  return cloned
}
