{"code": 0, "msg": "ok", "data": [{"id": "1924732347244400641", "parentId": "-1", "weight": 0, "name": "测试按钮", "path": "", "component": "", "meta": {"isLink": "", "isIframe": false, "isKeepAlive": false, "icon": "", "enName": "test-button", "isAffix": false, "title": "测试按钮", "isHide": false}, "sortOrder": 0, "menuType": "1", "permission": "work", "children": [{"id": "1924732784169242626", "parentId": "1924732347244400641", "weight": 0, "name": "测试按钮2", "path": "", "component": "", "meta": {"isLink": "", "isIframe": false, "isKeepAlive": false, "icon": "", "enName": "test-button2", "isAffix": false, "title": "测试按钮2", "isHide": false}, "sortOrder": 0, "menuType": "1", "permission": "work2"}]}, {"id": "1925107068962533377", "parentId": "-1", "weight": 0, "name": "页面1", "path": "/page1", "component": "", "meta": {"isLink": "", "isIframe": false, "isKeepAlive": false, "icon": "iconfont icon-shou<PERSON>_dongtaihui", "enName": "page1", "isAffix": false, "title": "页面1", "isHide": false}, "sortOrder": 0, "menuType": "0", "permission": "page1"}, {"id": "1925107143642116098", "parentId": "-1", "weight": 0, "name": "页面2", "path": "/page2", "component": "", "meta": {"isLink": "", "isIframe": false, "isKeepAlive": false, "icon": "home", "enName": "page2", "isAffix": false, "title": "页面2", "isHide": false}, "sortOrder": 0, "menuType": "0", "permission": "page2", "children": [{"id": "1925123011222630401", "parentId": "1925107143642116098", "weight": 0, "name": "页面2-1", "path": "/page2/page2-1", "component": "", "meta": {"isLink": "", "isIframe": false, "isKeepAlive": false, "icon": "", "enName": "page2-1", "isAffix": false, "title": "页面2-1", "isHide": false}, "sortOrder": 0, "menuType": "0", "permission": "page2:page2-1"}]}, {"id": "1925107288270106626", "parentId": "-1", "weight": 0, "name": "数据表", "path": "/test-table", "component": "", "meta": {"isLink": "", "isIframe": false, "isKeepAlive": false, "icon": "", "enName": "dataTable", "isAffix": false, "title": "数据表", "isHide": false}, "sortOrder": 0, "menuType": "0", "permission": "dataTable"}]}