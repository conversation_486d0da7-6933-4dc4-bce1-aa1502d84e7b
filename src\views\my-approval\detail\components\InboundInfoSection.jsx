import { Collapse, Descriptions, Table } from 'antd'
import { CaretRightOutlined } from '@ant-design/icons'

const { Panel } = Collapse

function InboundInfoSection({ data }) {
  const inboundInfoItems = [
    {
      key: 'inboundCategory',
      label: '入库类别',
      children: data?.inboundCategory || '-',
    },
    {
      key: 'inboundEquipment',
      label: '入库设备',
      children: data?.inboundEquipment || '-',
    },
    {
      key: 'inboundTubeCount',
      label: '入库管数',
      children: data?.inboundTubeCount || '-',
    },
    {
      key: 'inboundTime',
      label: '入库时间',
      children: data?.inboundTime || '-',
    },
    {
      key: 'inboundPerson',
      label: '入库人',
      children: data?.inboundPerson || '-',
    },
  ]

  const columns = [
    {
      title: '液氮罐',
      dataIndex: 'liquidNitrogenTank',
      key: 'liquidNitrogenTank',
    },
    {
      title: '提篮',
      dataIndex: 'basket',
      key: 'basket',
    },
    {
      title: '层数',
      dataIndex: 'layerNumber',
      key: 'layerNumber',
    },
    {
      title: '孔位',
      dataIndex: 'holePosition',
      key: 'holePosition',
    },
    {
      title: '管数',
      dataIndex: 'tubeCount',
      key: 'tubeCount',
    },
  ]

  return (
    <Collapse
      defaultActiveKey={['inboundInfo']}
      expandIcon={({ isActive }) => (
        <CaretRightOutlined rotate={isActive ? 90 : 0} />
      )}
      expandIconPosition="end">
      <Panel
        header={
          <span style={{ fontSize: '16px', fontWeight: 'bold' }}>入库信息</span>
        }
        key="inboundInfo">
        <Descriptions
          column={3}
          size="small"
          items={inboundInfoItems}
          style={{ marginBottom: 16 }}
        />

        <Table
          columns={columns}
          dataSource={data?.positionList || []}
          pagination={false}
          size="small"
          rowKey={(record, index) => index}
        />
      </Panel>
    </Collapse>
  )
}

export default InboundInfoSection
