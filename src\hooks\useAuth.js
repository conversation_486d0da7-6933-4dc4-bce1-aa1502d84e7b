import { useUserPermissions, useUserRoles } from '@/store/userStore'
import { authPermission, authRole } from '@/utils/auth'

/**
 * 验证用户是否具备某权限
 * @param {String} permission - 需要验证的权限
 * @return {Boolean} - 用户是否具备该权限
 */
export function useHasPermi(permission) {
  const permissions = useUserPermissions()
  return authPermission(permissions, permission)
}

/**
 * 验证用户是否含有指定权限，只需包含其中一个
 * @param {Array} permissions - 需要验证的权限列表
 * @return {Boolean} - 用户是否含有其中任一权限
 */
export function useHasPermiOr(permissions) {
  const userPermissions = useUserPermissions()
  return permissions.some((item) => {
    return authPermission(userPermissions, item)
  })
}
/**
 * 验证用户是否含有指定权限，必须全部拥有
 * @param {Array} permissions - 需要验证的权限列表
 * @return {Boolean} - 用户是否含有全部权限
 */
export function useHasPermiAnd(permissions) {
  const userPermissions = useUserPermissions()
  return permissions.every((item) => {
    return authPermission(userPermissions, item)
  })
}
/**
 * 验证用户是否具备某角色
 * @param {String} role - 需要验证的角色
 * @return {Boolean} - 用户是否具备该角色
 */
export function useHasRole(role) {
  const roles = useUserRoles()
  return authRole(roles, role)
}
/**
 * 验证用户是否含有指定角色，只需包含其中一个
 * @param {Array} roles - 需要验证的角色列表
 * @return {Boolean} - 用户是否含有其中任一角色
 */
export function useHasRoleOr(roles) {
  const userRoles = useUserRoles()
  return roles.some((item) => {
    return authRole(userRoles, item)
  })
}
/**
 * 验证用户是否含有指定角色，必须全部拥有
 * @param {Array} roles - 需要验证的角色列表
 * @return {Boolean} - 用户是否含有全部角色
 */
export function useHasRoleAnd(roles) {
  const userRoles = useUserRoles()
  return roles.every((item) => {
    return authRole(userRoles, item)
  })
}
