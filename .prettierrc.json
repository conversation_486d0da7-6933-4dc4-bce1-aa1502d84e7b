{"$schema": "https://json.schemastore.org/prettierrc", "plugins": ["prettier-plugin-css-order", "prettier-plugin-tailwindcss"], "tailwindStylesheet": "./src/assets/styles/tailwindecss.css", "cssDeclarationSorterOrder": "smacss", "tailwindFunctions": ["clsx"], "printWidth": 80, "tabWidth": 2, "singleQuote": true, "semi": false, "trailingComma": "all", "bracketSameLine": true, "bracketSpacing": true, "singleAttributePerLine": true, "proseWrap": "never", "endOfLine": "auto", "htmlWhitespaceSensitivity": "strict", "overrides": [{"files": ".prettier<PERSON>", "options": {"parser": "json"}}, {"files": ".lintstagedrc", "options": {"parser": "json"}}]}