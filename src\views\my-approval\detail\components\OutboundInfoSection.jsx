import { CaretRightOutlined } from '@ant-design/icons'
import { Button, Collapse, Table } from 'antd'
import { useNavigate } from 'react-router'

const { Panel } = Collapse

function OutboundInfoSection({ data }) {
  const navigate = useNavigate()

  const handleViewDetail = (record) => {
    // 跳转到出库管理-出库详情-质检页面
    navigate(`/outbound-management/detail/${record.id}?tab=quality`)
  }

  const columns = [
    {
      title: '出库时间',
      dataIndex: 'outboundTime',
      key: 'outboundTime',
    },
    {
      title: '出库管数',
      dataIndex: 'outboundTubeCount',
      key: 'outboundTubeCount',
    },
    {
      title: '细胞库级别',
      dataIndex: 'cellLibraryLevel',
      key: 'cellLibraryLevel',
    },
    {
      title: '出库人',
      dataIndex: 'outboundPerson',
      key: 'outboundPerson',
    },
    {
      title: '出库目的',
      dataIndex: 'outboundPurpose',
      key: 'outboundPurpose',
    },
    {
      title: '出库标识',
      dataIndex: 'outboundIdentifier',
      key: 'outboundIdentifier',
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Button
          type="link"
          size="small"
          onClick={() => handleViewDetail(record)}>
          查看详情
        </Button>
      ),
    },
  ]

  return (
    <Collapse
      defaultActiveKey={['outboundInfo']}
      expandIcon={({ isActive }) => (
        <CaretRightOutlined rotate={isActive ? 90 : 0} />
      )}
      expandIconPosition="end">
      <Panel
        header={
          <span style={{ fontSize: '16px', fontWeight: 'bold' }}>出库信息</span>
        }
        key="outboundInfo">
        <Table
          columns={columns}
          dataSource={data?.outboundList || []}
          pagination={false}
          size="small"
          rowKey={(record, index) => record.id || index}
        />
      </Panel>
    </Collapse>
  )
}

export default OutboundInfoSection
