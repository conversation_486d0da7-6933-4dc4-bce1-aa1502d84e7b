import { useLogout } from '@/store/userStore'
import { PoweroffOutlined, UserOutlined } from '@ant-design/icons'
import { Space } from 'antd'

function RightAction() {
  const logout = useLogout()

  return (
    <Space
      className="text-white"
      size={24}
      align="center">
      <Space
        size={10}
        align="center">
        <div className="flex h-[32px] w-[32px] items-center justify-center rounded-full bg-[rgba(255,255,255,0.05)] p-[9px]">
          <UserOutlined
            style={{
              fontSize: 16,
              color: '#fff',
            }}
          />
        </div>
        <span>管理员</span>
      </Space>
      <Space
        onClick={() => logout()}
        className="cursor-pointer hover:opacity-85"
        size={10}
        align="center">
        <div className="flex h-[32px] w-[32px] items-center justify-center rounded-full border border-white p-[8px]">
          <PoweroffOutlined
            style={{
              fontSize: 16,
              color: '#fff',
            }}
          />
        </div>
        <span>退出登录</span>
      </Space>
    </Space>
  )
}

export default RightAction
