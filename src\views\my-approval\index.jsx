import { getPendingApprovalCount } from '@/api/myApproval'
import Breadcrumb from '@/components/Breadcrumb'
import ThousandSeparator from '@/components/ThousandSeparator'
import Content from '@/layout/components/Content'
import {
  ExperimentOutlined,
  FileTextOutlined,
  InboxOutlined,
  SafetyOutlined,
} from '@ant-design/icons'
import { Card, Col, Row, Tabs } from 'antd'
import { useEffect, useState } from 'react'
import { useOutlet } from 'react-router'
import AmplificationApproval from './AmplificationApproval'
import MaterialApproval from './MaterialApproval'
import OutboundApproval from './OutboundApproval'
import QualityApproval from './QualityApproval'

function MyApproval() {
  const [activeKey, setActiveKey] = useState('1')
  const [overviewData, setOverviewData] = useState(null)

  const outlet = useOutlet()

  // 获取概览数据
  const fetchOverviewData = () => {
    getPendingApprovalCount().then((res) => {
      setOverviewData(res.data)
    })
  }

  useEffect(() => {
    fetchOverviewData()
  }, [])

  const handleTabChange = (key) => {
    setActiveKey(key)
  }

  // 概览卡片组件
  const OverviewCard = ({ title, icon, data, color }) => (
    <Card
      style={{
        borderRadius: '8px',
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
      }}>
      <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
        <div style={{ flex: 1 }}>
          <div
            style={{
              fontSize: '24px',
              fontWeight: 'bold',
            }}>
            <ThousandSeparator value={data?.pending || 0} />
          </div>
          <div
            style={{
              fontSize: '16px',
              color: '#262626',
              marginBottom: '4px',
            }}>
            {title}
          </div>
        </div>
        <div
          style={{
            width: '48px',
            height: '48px',
            borderRadius: '8px',
            backgroundColor: '#e6f4ff',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontSize: '24px',
            color: color || '#1890ff',
          }}>
          {icon}
        </div>
      </div>
    </Card>
  )

  if (outlet) {
    return outlet
  }

  return (
    <>
      <Breadcrumb />
      <Content>
        {/* 概览块 */}
        <div style={{ marginBottom: '16px' }}>
          <Row gutter={[16, 16]}>
            <Col span={6}>
              <OverviewCard
                title="质检"
                icon={<SafetyOutlined />}
                data={overviewData?.quality}
                color="#1890ff"
              />
            </Col>
            <Col span={6}>
              <OverviewCard
                title="扩增"
                icon={<ExperimentOutlined />}
                data={overviewData?.amplification}
                color="#1890ff"
              />
            </Col>
            <Col span={6}>
              <OverviewCard
                title="出库"
                icon={<InboxOutlined />}
                data={overviewData?.outbound}
                color="#1890ff"
              />
            </Col>
            <Col span={6}>
              <OverviewCard
                title="材料"
                icon={<FileTextOutlined />}
                data={overviewData?.material}
                color="#1890ff"
              />
            </Col>
          </Row>
        </div>

        {/* Tab页面 */}
        <Tabs
          activeKey={activeKey}
          onChange={handleTabChange}
          items={[
            {
              label: '质检',
              key: '1',
              children: <QualityApproval onDataChange={fetchOverviewData} />,
            },
            {
              label: '扩增',
              key: '2',
              children: (
                <AmplificationApproval onDataChange={fetchOverviewData} />
              ),
            },
            {
              label: '出库',
              key: '3',
              children: <OutboundApproval onDataChange={fetchOverviewData} />,
            },
            {
              label: '材料',
              key: '4',
              children: <MaterialApproval onDataChange={fetchOverviewData} />,
            },
          ]}
        />
      </Content>
    </>
  )
}

export default MyApproval
