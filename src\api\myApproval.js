import { get, post } from '@/utils/request/alova'

/**
 * 审核通过
 * @param {string} applyBid 申请记录主键
 * @returns
 */
export function approveApply(applyBid) {
  return get('/cell/apply/approveApply', { applyBid })
}

/**
 * 审核驳回
 * @param {string} applyBid 申请记录主键
 * @param {string} rejectReason 驳回原因
 * @returns
 */
export function rejectApply(applyBid, rejectReason) {
  return get('/cell/apply/rejectApply', { applyBid, rejectReason })
}

/**
 * 查询各个分类的待审批数量
 * @param {number} [applyType] 申请类型: 1-质检, 2-扩增, 3-出库, 4-材料，不传则返回所有类型统计
 * @returns
 */
export function getPendingApprovalCount(applyType) {
  return get(
    '/cell/apply/getPendingApprovalCount',
    applyType ? { applyType } : {},
  )
}

/**
 * 查询材料审批详情
 * @param {string} applyBid 申请记录主键
 * @returns
 */
export function getMaterialApplyInfoDetail(applyBid) {
  return get('/cell/apply/getMaterialApplyInfoDetail', { applyBid })
}

/**
 * 查询质检、扩增、出库申请/审批详情
 * @param {string} applyBid 申请记录主键
 * @returns
 */
export function getCellApplyInfoDetail(applyBid) {
  return get('/cell/apply/getCellApplyInfoDetail', { applyBid })
}

/**
 * 分页查询材料申请记录
 * @param {Object} data 查询参数
 * @param {Object} data.page 分页参数
 * @param {Array} data.page.orders 排序参数
 * @param {number} data.page.pageNum 页码
 * @param {number} data.page.pageSize 每页大小
 * @param {Object} data.params 查询条件
 * @param {string} data.params.applier 申请人
 * @param {string} data.params.applyTimeEnd 申请时间结束
 * @param {string} data.params.applyTimeStart 申请时间开始
 * @param {string} data.params.business 业务
 * @param {string} data.params.category 类别
 * @param {string} data.params.material 材料
 * @param {string} data.params.orderNo 订单号
 * @returns
 */
export function selectMaterialApplyRecordPage(data) {
  return post('/cell/apply/selectMaterialApplyRecordPage', data, {
    transformType: 'proTable',
  })
}

/**
 * 分页查询质检、扩增、出库申请/审批记录
 * @param {Object} data 查询参数
 * @param {Object} data.page 分页参数
 * @param {Array} data.page.orders 排序参数
 * @param {number} data.page.pageNum 页码
 * @param {number} data.page.pageSize 每页大小
 * @param {Object} data.params 查询条件
 * @param {number} data.params.applyType 申请类型: 1-质检, 2-扩增, 3-出库
 * @param {string} data.params.approveTimeEnd 通过时间范围：结束
 * @param {string} data.params.approveTimeStart 通过时间范围：开始
 * @param {string} data.params.cellCode 细胞中心编号
 * @param {string} data.params.cellName 细胞名称（中英文模糊匹配）
 * @param {string} data.params.createdAtEnd 申请时间范围：结束
 * @param {string} data.params.createdAtStart 申请时间范围：开始
 * @param {string} data.params.cryoBatchNo 冻存批次号
 * @param {string} data.params.originalBatchNo 原始批次号
 * @param {string} data.params.sourceOrgan 细胞来源
 * @param {string} data.params.stockInNo 入库订单号
 * @param {string} data.params.storageLibraryBid 细胞库级别（存储库主键）
 * @returns
 */
export function selectCellApplyRecordPage(data) {
  return post('/cell/apply/selectCellApplyRecordPage', data, {
    transformType: 'proTable',
  })
}
