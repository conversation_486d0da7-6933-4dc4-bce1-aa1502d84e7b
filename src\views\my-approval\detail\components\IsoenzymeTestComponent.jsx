import { Descriptions } from 'antd'

// 同工酶检测组件
function IsoenzymeTestComponent({ data }) {
  return (
    <Descriptions
      column={3}
      size="small"
      items={[
        {
          key: 'enzymePattern',
          label: '酶谱',
          children: data?.enzymePattern || '-',
        },
        {
          key: 'testResult',
          label: '检测结果',
          children: data?.testResult || '-',
        },
        {
          key: 'testMethod',
          label: '检测方法',
          children: data?.testMethod || '-',
        },
      ]}
    />
  )
}

export default IsoenzymeTestComponent
