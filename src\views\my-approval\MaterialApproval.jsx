import {
  approveApply,
  rejectApply,
  selectMaterialApplyRecordPage,
} from '@/api/myApproval'
import tableProStyles from '@/assets/styles/ant-pro.module.scss'
import ColToggleButton from '@/components/Buttons/ColToggleButton'
import { useRouter } from '@/hooks/router'
import { ProTable } from '@ant-design/pro-components'
import { Button, Input, message, Modal, Progress, Segmented, Space } from 'antd'
import { useRef, useState } from 'react'
import { getMaterialColumns } from './materialColumns'

const { TextArea } = Input

function MaterialApproval({ onDataChange }) {
  const [rejectModalVisible, setRejectModalVisible] = useState(false)
  const [exportModalVisible, setExportModalVisible] = useState(false)
  const [currentRecord, setCurrentRecord] = useState(null)
  const [rejectReason, setRejectReason] = useState('')
  const [exportProgress, setExportProgress] = useState(0)
  const [activeTab, setActiveTab] = useState('pending') // pending: 待审批, record: 审批记录
  const [messageApi, contextHolder] = message.useMessage()
  const { push } = useRouter()
  const ref = useRef(null)

  // 操作按钮渲染
  const renderActions = (text, record) => {
    const actions = []

    // 查看详情按钮
    actions.push(
      <a
        key="detail"
        onClick={() => push(`/my-approval/report-approval/${record.bid}`)}>
        查看详情
      </a>,
    )

    // 待审批状态显示审批按钮
    if (activeTab === 'pending' && record.status === 0) {
      actions.push(
        <a
          key="pass"
          onClick={() => handleApprove(record)}>
          通过
        </a>,
        <a
          key="reject"
          onClick={() => handleReject(record)}>
          驳回
        </a>,
      )
    }

    return <Space>{actions}</Space>
  }

  // 审批通过处理
  const handleApprove = async (record) => {
    try {
      await approveApply(record.bid)
      messageApi.success('审批通过成功')
      ref.current?.reload()
      // 通知父组件刷新概览数据
      onDataChange?.()
    } catch (error) {
      console.log(error)
    }
  }

  // 审批驳回处理
  const handleReject = (record) => {
    setCurrentRecord(record)
    setRejectReason('')
    setRejectModalVisible(true)
  }

  // 确认驳回
  const confirmReject = async () => {
    if (!rejectReason.trim()) {
      messageApi.warning('请填写驳回原因')
      return
    }
    try {
      await rejectApply(currentRecord.bid, rejectReason.trim())
      messageApi.success('审批驳回成功')
      setRejectModalVisible(false)
      ref.current?.reload()
      // 通知父组件刷新概览数据
      onDataChange?.()
    } catch (error) {
      console.log(error)
    }
  }

  // 导出清单处理
  const handleExport = () => {
    setExportProgress(0)
    setExportModalVisible(true)

    // 模拟导出进度
    const timer = setInterval(() => {
      setExportProgress((prev) => {
        if (prev >= 100) {
          clearInterval(timer)
          setTimeout(() => {
            setExportModalVisible(false)
            messageApi.success('导出成功')
          }, 500)
          return 100
        }
        return prev + 10
      })
    }, 200)
  }

  // 材料审批表格列配置
  const materialColumns = [
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      width: 180,
      fixed: 'right',
      render: renderActions,
    },
    ...getMaterialColumns().filter((col) => {
      // 根据Tab页过滤列
      if (activeTab === 'pending') {
        return col.key !== 'approveTime' && col.key !== 'status'
      }
      return true
    }),
  ]

  // 获取数据的API函数 - 统一使用selectMaterialApplyRecordPage
  const getDataApi = selectMaterialApplyRecordPage

  return (
    <>
      {contextHolder}
      <div style={{ marginBottom: 16 }}>
        <Segmented
          value={activeTab}
          onChange={setActiveTab}
          options={[
            { label: '待审批', value: 'pending' },
            { label: '审批记录', value: 'record' },
          ]}
          size="large"
        />
      </div>

      <ProTable
        className={tableProStyles.ghost_table_pro}
        columns={materialColumns}
        actionRef={ref}
        ghost={true}
        scroll={{ x: 2500 }}
        request={async (params) => {
          const { current, pageSize, ...searchParams } = params
          const requestData = {
            page: {
              orders: [
                {
                  asc: false,
                  field: 'createdAt',
                },
              ],
              pageNum: current || 1,
              pageSize: pageSize || 10,
            },
            params: {
              ...searchParams,
              // 根据activeTab设置状态过滤
              status: activeTab === 'pending' ? 0 : undefined,
            },
          }
          const res = await getDataApi(requestData)
          return res
        }}
        form={{
          name: 'materialApproval',
          syncToUrl: (values, type) => {
            if (type === 'get') {
              return {
                ...values,
                applyTime: [values.applyTimeStart, values.applyTimeEnd],
                approveTime: [values.approveTimeStart, values.approveTimeEnd],
              }
            }
            return values
          },
        }}
        columnsState={{
          persistenceKey: 'material-approval-table',
          persistenceType: 'localStorage',
          defaultValue: {
            option: { fixed: 'right', disable: true },
          },
        }}
        rowKey="bid"
        search={{
          labelWidth: 'auto',
        }}
        options={{
          reload: false,
          density: false,
          setting: {
            children: <ColToggleButton />,
          },
        }}
        pagination={{
          size: 'default',
          pageSize: 10,
          showSizeChanger: true,
          showQuickJumper: true,
        }}
        headerTitle={
          <Button
            type="primary"
            onClick={handleExport}>
            导出清单
          </Button>
        }
      />

      {/* 驳回原因弹窗 */}
      <Modal
        title="驳回原因"
        open={rejectModalVisible}
        onOk={confirmReject}
        onCancel={() => setRejectModalVisible(false)}
        okText="确认"
        cancelText="取消"
        width={500}>
        <div style={{ marginBottom: 16 }}>
          <p>请填写驳回原因：</p>
        </div>
        <TextArea
          rows={4}
          value={rejectReason}
          onChange={(e) => setRejectReason(e.target.value)}
          placeholder="请输入驳回原因"
          maxLength={500}
          showCount
        />
      </Modal>

      {/* 导出进度弹窗 */}
      <Modal
        title="导出清单"
        open={exportModalVisible}
        footer={null}
        closable={false}
        width={400}>
        <div style={{ textAlign: 'center', padding: '20px 0' }}>
          <Progress
            type="circle"
            percent={exportProgress}
            status={exportProgress === 100 ? 'success' : 'active'}
          />
          <div style={{ marginTop: '16px', color: '#666' }}>
            {exportProgress === 100 ? '导出完成' : '正在导出中...'}
          </div>
        </div>
      </Modal>
    </>
  )
}

export default MaterialApproval
