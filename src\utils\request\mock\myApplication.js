import { defineMock } from '@alova/mock'

// 质检列表模拟数据 - 根据接口文档字段重新定义
const mockQualityInspectionData = [
  {
    bid: 'apply_001',
    id: 1,
    status: 0, // 0-待审批, 1-审批通过, 2-审批驳回, 3-检测中, 4-检测完成, 5-已完成
    applyType: 1, // 1-质检, 2-扩增, 3-出库
    cellCode: 'CC001',
    cellNameCn: 'HeLa细胞',
    cellNameEn: 'HeLa',
    cellGeneration: 'P5',
    cellType: '贴壁细胞',
    cryoBatchNo: 'FB001',
    originalBatchNo: 'OB001',
    sourceOrgan: '宫颈癌细胞系',
    stockInNo: 'RK202501001',
    storageLibraryLevel: '一级库',
    tubeNum: 50,
    currentNum: 45,
    cultureCondition: '37℃,5%CO2',
    medium: 'DMEM+10%FBS',
    recoveryTime: '2025-01-15T10:30:00',
    recoveryViability: '95%',
    remarks: '质检申请备注信息',
    checkHistory: 1,
    checkTime: null,
    analysisItemText: 'STR,支原体PCR,细菌真菌',
    strFlag: '待检测',
    strRemark: '',
    mycoPcr: '待检测',
    mycoCulture: '待检测',
    mycoDna: '待检测',
    speciesPcr: '待检测',
    fungusLowTemp: '待检测',
    fungusHighTemp: '待检测',
    dnaBarcoding: '待检测',
    applierName: '申请人A',
    approverName: '',
    approveTime: null,
    rejectReason: '',
  },
  {
    bid: 'apply_002',
    id: 2,
    status: 3, // 检测中
    applyType: 1,
    cellCode: 'CC002',
    cellNameCn: 'A549细胞',
    cellNameEn: 'A549',
    cellGeneration: 'P3',
    cellType: '贴壁细胞',
    cryoBatchNo: 'FB002',
    originalBatchNo: 'OB002',
    sourceOrgan: '肺癌细胞系',
    stockInNo: 'RK202501002',
    storageLibraryLevel: '二级库',
    tubeNum: 30,
    currentNum: 28,
    cultureCondition: '37℃,5%CO2',
    medium: 'F12K+10%FBS',
    recoveryTime: '2025-01-14T14:20:00',
    recoveryViability: '92%',
    remarks: '紧急质检',
    checkHistory: 2,
    checkTime: '2025-01-15T09:00:00',
    analysisItemText: 'STR,支原体PCR',
    strFlag: '检测中',
    strRemark: '',
    mycoPcr: '检测中',
    mycoCulture: '阴性',
    mycoDna: '阴性',
    speciesPcr: '人源',
    fungusLowTemp: '阴性',
    fungusHighTemp: '阴性',
    dnaBarcoding: '待检测',
    applierName: '申请人B',
    approverName: '张三',
    approveTime: '2025-01-14T16:00:00',
    rejectReason: '',
  },
  {
    bid: 'apply_003',
    id: 3,
    status: 2, // 审批驳回
    applyType: 1,
    cellCode: 'CC003',
    cellNameCn: 'MCF-7细胞',
    cellNameEn: 'MCF-7',
    cellGeneration: 'P8',
    cellType: '贴壁细胞',
    cryoBatchNo: 'FB003',
    originalBatchNo: 'OB003',
    sourceOrgan: '乳腺癌细胞系',
    stockInNo: 'RK202501003',
    storageLibraryLevel: '一级库',
    tubeNum: 25,
    currentNum: 23,
    cultureCondition: '37℃,5%CO2',
    medium: 'DMEM+10%FBS',
    recoveryTime: '2025-01-13T16:45:00',
    recoveryViability: '88%',
    remarks: '常规质检',
    checkHistory: 1,
    checkTime: '2025-01-14T15:30:00',
    analysisItemText: 'STR,支原体PCR,细菌真菌',
    strFlag: '异常',
    strRemark: 'STR图谱异常，需重新检测',
    mycoPcr: '阴性',
    mycoCulture: '阴性',
    mycoDna: '阴性',
    speciesPcr: '人源',
    fungusLowTemp: '阴性',
    fungusHighTemp: '阴性',
    dnaBarcoding: '正常',
    applierName: '申请人C',
    approverName: '李四',
    approveTime: '2025-01-14T17:00:00',
    rejectReason: 'STR检测结果异常，请重新申请',
  },
  {
    bid: 'apply_004',
    id: 4,
    status: 4, // 检测完成
    applyType: 1,
    cellCode: 'CC004',
    cellNameCn: 'HEK293细胞',
    cellNameEn: 'HEK293',
    cellGeneration: 'P6',
    cellType: '贴壁细胞',
    cryoBatchNo: 'FB004',
    originalBatchNo: 'OB004',
    sourceOrgan: '胚胎肾细胞系',
    stockInNo: 'RK202501004',
    storageLibraryLevel: '二级库',
    tubeNum: 40,
    currentNum: 38,
    cultureCondition: '37℃,5%CO2',
    medium: 'DMEM+10%FBS',
    recoveryTime: '2025-01-12T11:15:00',
    recoveryViability: '96%',
    remarks: '质量控制检测',
    checkHistory: 1,
    checkTime: '2025-01-13T14:20:00',
    analysisItemText: 'STR,支原体PCR',
    strFlag: '是',
    strRemark: 'STR图谱正常',
    mycoPcr: '阴性',
    mycoCulture: '阴性',
    mycoDna: '阴性',
    speciesPcr: '人源',
    fungusLowTemp: '阴性',
    fungusHighTemp: '阴性',
    dnaBarcoding: '正常',
    applierName: '申请人D',
    approverName: '王五',
    approveTime: '2025-01-13T16:30:00',
    rejectReason: '',
  },
  {
    bid: 'apply_005',
    id: 5,
    status: 5, // 已完成
    applyType: 1,
    cellCode: 'CC005',
    cellNameCn: 'U87细胞',
    cellNameEn: 'U87',
    cellGeneration: 'P4',
    cellType: '贴壁细胞',
    cryoBatchNo: 'FB005',
    originalBatchNo: 'OB005',
    sourceOrgan: '胶质瘤细胞系',
    stockInNo: 'RK202501005',
    storageLibraryLevel: '三级库',
    tubeNum: 35,
    currentNum: 33,
    cultureCondition: '37℃,5%CO2',
    medium: 'MEM+10%FBS',
    recoveryTime: '2025-01-11T09:30:00',
    recoveryViability: '94%',
    remarks: '入库前质检',
    checkHistory: 1,
    checkTime: '2025-01-12T10:15:00',
    analysisItemText: 'STR,支原体PCR,细菌真菌',
    strFlag: '是',
    strRemark: 'STR图谱正常',
    mycoPcr: '阴性',
    mycoCulture: '阴性',
    mycoDna: '阴性',
    speciesPcr: '人源',
    fungusLowTemp: '阴性',
    fungusHighTemp: '阴性',
    dnaBarcoding: '正常',
    applierName: '申请人E',
    approverName: '赵六',
    approveTime: '2025-01-12T14:45:00',
    rejectReason: '',
  },
  // 添加一些出库申请类型的数据用于测试
  {
    bid: 'outbound_001',
    id: 51,
    status: 2, // 审批驳回
    applyType: 3, // 出库申请
    cellCode: 'CC051',
    cellNameCn: 'HeLa出库细胞',
    cellNameEn: 'HeLa Outbound',
    cellGeneration: 'P5',
    cellType: '贴壁细胞',
    cryoBatchNo: 'FB051',
    originalBatchNo: 'OB051',
    sourceOrgan: '宫颈癌细胞系',
    stockInNo: 'RK202501051',
    outboundOrderNo: 'OUT202501001',
    storageLibraryLevel: '一级库',
    tubeNum: 50,
    currentNum: 45,
    outboundTubeNum: 5,
    outboundPerson: '出库员A',
    outboundPurpose: '科研使用',
    outboundFlag: 'OUT001',
    frozenTubeNum: 45,
    recoveryTubeNum: 5,
    cultureCondition: '37℃,5%CO2',
    medium: 'DMEM+10%FBS',
    recoveryTime: '2025-01-15T10:30:00',
    recoveryViability: '95%',
    remarks: '出库申请备注信息',
    checkHistory: 1,
    checkTime: '2025-01-15T14:30:00',
    analysisItemText: 'STR,支原体PCR',
    strFlag: '异常',
    strRemark: '出库前检测异常',
    mycoPcr: '阴性',
    mycoCulture: '阴性',
    mycoDna: '阴性',
    speciesPcr: '人源',
    fungusLowTemp: '阴性',
    fungusHighTemp: '阴性',
    dnaBarcoding: '正常',
    applierName: '申请人F',
    approverName: '审核员A',
    approveTime: '2025-01-15T16:00:00',
    rejectReason: '出库前质检不合格，请重新申请',
    stockInPerson: '入库员A',
    qualityInspector: '质检员A',
  },
  {
    bid: 'outbound_002',
    id: 52,
    status: 2, // 审批驳回
    applyType: 3, // 出库申请
    cellCode: 'CC052',
    cellNameCn: 'A549出库细胞',
    cellNameEn: 'A549 Outbound',
    cellGeneration: 'P3',
    cellType: '贴壁细胞',
    cryoBatchNo: 'FB052',
    originalBatchNo: 'OB052',
    sourceOrgan: '肺癌细胞系',
    stockInNo: 'RK202501052',
    outboundOrderNo: 'OUT202501002',
    storageLibraryLevel: '二级库',
    tubeNum: 30,
    currentNum: 28,
    outboundTubeNum: 2,
    outboundPerson: '出库员B',
    outboundPurpose: '临床试验',
    outboundFlag: 'OUT002',
    frozenTubeNum: 28,
    recoveryTubeNum: 2,
    cultureCondition: '37℃,5%CO2',
    medium: 'F12K+10%FBS',
    recoveryTime: '2025-01-14T14:20:00',
    recoveryViability: '92%',
    remarks: '紧急出库申请',
    checkHistory: 2,
    checkTime: '2025-01-14T16:00:00',
    analysisItemText: 'STR,支原体PCR',
    strFlag: '待检测',
    strRemark: '',
    mycoPcr: '待检测',
    mycoCulture: '阴性',
    mycoDna: '阴性',
    speciesPcr: '人源',
    fungusLowTemp: '阴性',
    fungusHighTemp: '阴性',
    dnaBarcoding: '待检测',
    applierName: '申请人G',
    approverName: '审核员B',
    approveTime: '2025-01-14T18:00:00',
    rejectReason: '申请材料不完整，请补充相关文件',
    stockInPerson: '入库员B',
    qualityInspector: '质检员B',
  },
]

// 生成更多模拟数据
for (let i = 6; i <= 50; i++) {
  const statuses = [0, 1, 2, 3, 4, 5] // 0-待审批, 1-审批通过, 2-审批驳回, 3-检测中, 4-检测完成, 5-已完成
  const cellTypes = ['贴壁细胞', '悬浮细胞']
  const mediums = [
    'DMEM+10%FBS',
    'F12K+10%FBS',
    'MEM+10%FBS',
    'RPMI1640+10%FBS',
  ]
  const libraries = ['一级库', '二级库', '三级库']

  const status = statuses[Math.floor(Math.random() * statuses.length)]

  mockQualityInspectionData.push({
    bid: `apply_${String(i).padStart(3, '0')}`,
    id: i,
    status: status,
    applyType: 1,
    cellCode: `CC${String(i).padStart(3, '0')}`,
    cellNameCn: `细胞${i}`,
    cellNameEn: `Cell${i}`,
    cellGeneration: `P${Math.floor(Math.random() * 10) + 1}`,
    cellType: cellTypes[Math.floor(Math.random() * cellTypes.length)],
    cryoBatchNo: `FB${String(i).padStart(3, '0')}`,
    originalBatchNo: `OB${String(i).padStart(3, '0')}`,
    sourceOrgan: '细胞系',
    stockInNo: `RK20250${String(i).padStart(4, '0')}`,
    storageLibraryLevel:
      libraries[Math.floor(Math.random() * libraries.length)],
    tubeNum: Math.floor(Math.random() * 50) + 10,
    currentNum: Math.floor(Math.random() * 45) + 10,
    cultureCondition: '37℃,5%CO2',
    medium: mediums[Math.floor(Math.random() * mediums.length)],
    recoveryTime: `2025-01-${String(Math.floor(Math.random() * 15) + 1).padStart(2, '0')}T${String(Math.floor(Math.random() * 12) + 8).padStart(2, '0')}:${String(Math.floor(Math.random() * 60)).padStart(2, '0')}:00`,
    recoveryViability: `${Math.floor(Math.random() * 10) + 90}%`,
    remarks: `质检申请备注${i}`,
    checkHistory: Math.floor(Math.random() * 3) + 1,
    checkTime:
      status >= 3
        ? `2025-01-${String(Math.floor(Math.random() * 15) + 1).padStart(2, '0')}T${String(Math.floor(Math.random() * 12) + 8).padStart(2, '0')}:${String(Math.floor(Math.random() * 60)).padStart(2, '0')}:00`
        : null,
    analysisItemText: 'STR,支原体PCR',
    strFlag: status >= 4 ? '是' : '待检测',
    strRemark: '',
    mycoPcr: status >= 4 ? '阴性' : '待检测',
    mycoCulture: status >= 4 ? '阴性' : '待检测',
    mycoDna: status >= 4 ? '阴性' : '待检测',
    speciesPcr: status >= 4 ? '人源' : '待检测',
    fungusLowTemp: status >= 4 ? '阴性' : '待检测',
    fungusHighTemp: status >= 4 ? '阴性' : '待检测',
    dnaBarcoding: status >= 4 ? '正常' : '待检测',
    applierName: `申请人${String.fromCharCode(65 + (i % 26))}`,
    approverName: status >= 1 ? '审核员' : '',
    approveTime:
      status >= 1
        ? `2025-01-${String(Math.floor(Math.random() * 15) + 1).padStart(2, '0')}T${String(Math.floor(Math.random() * 12) + 8).padStart(2, '0')}:${String(Math.floor(Math.random() * 60)).padStart(2, '0')}:00`
        : null,
    rejectReason: status === 2 ? '检测条件不符合要求' : '',
  })
}

const myApplicationMock = defineMock(
  {
    // 分页查询质检申请记录 - 使用接口文档中的接口
    '[POST]/cell/apply/selectCellApplyRecordPage': ({ data }) => {
      const requestData = data
      // 根据接口文档格式解析参数
      const page = requestData?.page?.pageNum || 1
      const pageSize = requestData?.page?.pageSize || 10
      const params = requestData?.params || {}

      // 过滤数据 - 根据applyType参数过滤
      let filteredData = mockQualityInspectionData
      if (params.applyType) {
        filteredData = filteredData.filter(
          (item) => item.applyType === params.applyType,
        )
      }

      // 根据查询条件过滤
      if (params.stockInNo) {
        filteredData = filteredData.filter((item) =>
          item.stockInNo.includes(params.stockInNo),
        )
      }

      if (params.cellCode) {
        filteredData = filteredData.filter((item) =>
          item.cellCode.includes(params.cellCode),
        )
      }

      if (params.cellName) {
        filteredData = filteredData.filter(
          (item) =>
            item.cellNameCn.includes(params.cellName) ||
            item.cellNameEn.includes(params.cellName),
        )
      }

      if (params.sourceOrgan) {
        filteredData = filteredData.filter((item) =>
          item.sourceOrgan.includes(params.sourceOrgan),
        )
      }

      if (params.originalBatchNo) {
        filteredData = filteredData.filter((item) =>
          item.originalBatchNo.includes(params.originalBatchNo),
        )
      }

      if (params.cryoBatchNo) {
        filteredData = filteredData.filter((item) =>
          item.cryoBatchNo.includes(params.cryoBatchNo),
        )
      }

      if (params.storageLibraryBid) {
        filteredData = filteredData.filter(
          (item) => item.storageLibraryLevel === params.storageLibraryBid,
        )
      }

      // 时间范围过滤
      if (params.approveTimeStart && params.approveTimeEnd) {
        filteredData = filteredData.filter((item) => {
          if (!item.approveTime) return false
          const approveTime = new Date(item.approveTime)
          const startTime = new Date(params.approveTimeStart)
          const endTime = new Date(params.approveTimeEnd)
          return approveTime >= startTime && approveTime <= endTime
        })
      }

      if (params.createdAtStart && params.createdAtEnd) {
        filteredData = filteredData.filter((item) => {
          if (!item.recoveryTime) return false
          const recoveryTime = new Date(item.recoveryTime)
          const startTime = new Date(params.createdAtStart)
          const endTime = new Date(params.createdAtEnd)
          return recoveryTime >= startTime && recoveryTime <= endTime
        })
      }

      // 计算分页数据
      const start = (page - 1) * pageSize
      const end = start + pageSize
      const _data = filteredData.slice(start, end)

      return {
        code: 0,
        data: {
          page,
          pageSize,
          total: filteredData.length,
          list: _data,
          hasNext: end < filteredData.length,
        },
        msg: '成功',
      }
    },

    // 查询质检申请详情
    '/cell/apply/getCellApplyInfoDetail': ({ query }) => {
      const applyBid = query.applyBid
      const item = mockQualityInspectionData.find(
        (item) => item.bid === applyBid,
      )

      if (item) {
        // 根据接口文档返回详情数据结构
        return {
          code: 0,
          data: {
            cellBid: item.bid,
            cellCode: item.cellCode,
            cellGeneration: item.cellGeneration,
            cellNameCn: item.cellNameCn,
            cellNameEn: item.cellNameEn,
            cellSpecies: 1, // 1-人，2-小鼠
            cellType: item.cellType,
            cryoBatchNo: item.cryoBatchNo,
            cultureCondition: item.cultureCondition,
            currentNum: item.currentNum,
            medium: item.medium,
            originalBatchNo: item.originalBatchNo,
            outStockPerson: '',
            outStockTime: null,
            relatedBusinessBid: item.bid,
            relatedBusinessDetailBid: item.bid,
            relatedBusinessType: 1, // 1-细胞质检
            remarks: item.remarks,
            sourceOrgan: item.sourceOrgan,
            stockInBid: item.stockInNo,
            stockInNo: item.stockInNo,
            stockInPerson: item.applierName,
            stockInStatus: 1, // 已入库确认
            stockInTime: item.recoveryTime,
            analysisItem: item.analysisItemText,
            tubeNum: item.tubeNum,
            // 检测历史
            detectionHistoryList: [],
            // 检测信息
            detectionInfoList: [
              {
                analysisItem: 1, // STR
                analysisResult: item.strFlag,
              },
              {
                analysisItem: 6, // 支原体PCR
                analysisResult: item.mycoPcr,
              },
            ],
            // 入库明细
            stockInDetailList: [
              {
                basket: 'A1',
                deviceDetailBid: 'device_001',
                floor: '1',
                holePositions: '1,2,3',
                liquidNitrogenTank: 'Tank-001',
                stockInBid: item.stockInNo,
                stockInPerson: item.applierName,
                stockInTime: item.recoveryTime,
                storageLibraryBid: 'lib_001',
                storageLibraryName: item.storageLibraryLevel,
              },
            ],
            // 出库明细
            stockOutDetailList: [],
          },
          msg: '成功',
        }
      } else {
        return {
          code: 404,
          data: null,
          msg: '数据不存在',
        }
      }
    },
    // 放弃入库
    '[POST]/quality-inspection/abandon-storage': () => {
      return {
        code: 0,
        data: {
          message: '放弃入库操作成功',
        },
        msg: '成功',
      }
    },

    // 申请复检
    '[POST]/quality-inspection/retest': () => {
      return {
        code: 0,
        data: {
          message: '申请复检成功',
        },
        msg: '成功',
      }
    },

    // 提交质检申请
    '[POST]/cell/apply/submitQualityApply': ({ data }) => {
      const requestData = data

      // 生成新的申请记录
      const newApply = {
        bid: `apply_${Date.now()}`,
        id: mockQualityInspectionData.length + 1,
        status: 0, // 待审批
        applyType: 1, // 质检申请
        cellCode: requestData.cellCenterCode || '',
        cellNameCn: requestData.cellChineseName || '',
        cellNameEn: requestData.cellEnglishName || '',
        cellGeneration: requestData.cellGeneration || '',
        cellType: requestData.cellType || '',
        cryoBatchNo: `FB${String(mockQualityInspectionData.length + 1).padStart(3, '0')}`,
        originalBatchNo: `OB${String(mockQualityInspectionData.length + 1).padStart(3, '0')}`,
        sourceOrgan: requestData.cellSource || '',
        stockInNo: `RK${Date.now()}`,
        storageLibraryLevel: '一级库',
        tubeNum: 50,
        currentNum: 50,
        cultureCondition: requestData.cultureCondition || '',
        medium: requestData.cultureBase || '',
        recoveryTime: requestData.receiveTime || new Date().toISOString(),
        recoveryViability: '95%',
        remarks: requestData.remark || '',
        checkHistory: 1,
        checkTime: null,
        analysisItemText: [
          ...(requestData.crossContamination || []).map((val) => {
            const options = {
              1: 'STR',
              2: '同工酶',
              3: 'PCR种属鉴定',
              4: 'DNA Barcoding',
            }
            return options[val]
          }),
          ...(requestData.exogenousContamination || []).map((val) => {
            const options = {
              5: '支原体培养法',
              6: '支原体PCR检测法',
              7: '支原体DNA染色法',
              8: '细菌真菌接种法',
            }
            return options[val]
          }),
        ]
          .filter(Boolean)
          .join(','),
        strFlag: '待检测',
        strRemark: '',
        mycoPcr: '待检测',
        mycoCulture: '待检测',
        mycoDna: '待检测',
        speciesPcr: '待检测',
        fungusLowTemp: '待检测',
        fungusHighTemp: '待检测',
        dnaBarcoding: '待检测',
        applierName: '当前用户',
        approverName: '',
        approveTime: null,
        rejectReason: '',
      }

      // 添加到模拟数据中
      mockQualityInspectionData.push(newApply)

      return {
        code: 0,
        data: {
          applyBid: newApply.bid,
          message: '质检申请提交成功',
        },
        msg: '成功',
      }
    },

    // 重新发起出库申请
    '[POST]/cell/apply/reapplyOutbound': ({ data }) => {
      return new Promise((resolve) => {
        setTimeout(() => {
          const { applyBid } = data
          const record = mockQualityInspectionData.find(
            (item) => item.bid === applyBid,
          )

          if (record && record.status === 2) {
            // 只有驳回状态才能重新发起
            // 创建新的申请记录
            const newApply = {
              ...record,
              bid: `apply_${Date.now()}`,
              id: mockQualityInspectionData.length + 1,
              status: 0, // 重置为待审批
              approverName: '',
              approveTime: null,
              rejectReason: '',
              applierName: '当前用户',
            }

            // 标记原记录已重新发起
            record.reapplied = true

            // 添加新记录到列表开头
            mockQualityInspectionData.unshift(newApply)

            resolve({
              code: 0,
              data: {
                newApplyBid: newApply.bid,
              },
              msg: '重新发起申请成功',
            })
          } else {
            resolve({
              code: 400,
              data: null,
              msg: '该申请无法重新发起',
            })
          }
        }, 1000)
      })
    },
  },
  true,
)

console.log(myApplicationMock, 'myApplicationMock')

export default myApplicationMock
