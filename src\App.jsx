import { RouterProvider } from 'react-router'

import { useBrowserRouter, useWhiteRoute } from '@/router'
import InitInfo from './layout/initInfo'
import AuthElement from './router/AuthElement'

function BrowserRouter() {
  const browserRouter = useBrowserRouter()
  console.log('BrowserRouter render', browserRouter)
  return <RouterProvider router={browserRouter} />
}

function AuthApp() {
  return (
    <AuthElement>
      <InitInfo>
        <BrowserRouter />
      </InitInfo>
    </AuthElement>
  )
}

function App() {
  const isWhiteRoute = useWhiteRoute()
  // 白名单的路由直接渲染，否则走认证流程
  return isWhiteRoute ? <BrowserRouter /> : <AuthApp />
}

export default App
