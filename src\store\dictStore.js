import { getDictBy<PERSON>ey } from '@/api/common'
import { enableMapSet } from 'immer'
import { useEffect, useMemo } from 'react'
import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import { immer } from 'zustand/middleware/immer'

// 初始化state
const initState = {
  dicts: new Map(), // 使用Map提高查找效率
  loading: new Set(), // 记录正在加载的字典类型
}

enableMapSet()
const store = immer((set, get) => ({
  ...initState,
  actions: {
    /**
     * 获取字典数据
     * @param {String} dictType 字典类型
     * @returns {Array} 字典数据
     */
    getDict(dictType) {
      if (!dictType) {
        return null
      }
      return get().dicts.get(dictType) || null
    },

    /**
     * 设置字典
     * @param {String} dictType 字典类型
     * @param {Array[Object]} value 字典数据
     */
    setDict(dictType, value) {
      if (dictType && value) {
        set((state) => {
          state.dicts.set(dictType, value)
        })
      }
    },

    /**
     * 删除指定字典
     * @param {String} dictType 字典类型
     * @returns {Boolean} 是否删除成功
     */
    removeDict(dictType) {
      let success = false
      set((state) => {
        if (dictType && state.dicts.has(dictType)) {
          state.dicts.delete(dictType)
          success = true
        }
      })
      return success
    },

    /**
     * 清空所有字典
     */
    cleanDict() {
      set((state) => {
        state.dicts.clear()
        state.loading.clear()
      })
    },

    /**
     * 检查字典是否存在
     * @param {String} dictType 字典类型
     * @returns {Boolean}
     */
    hasDict(dictType) {
      return get().dicts.has(dictType)
    },

    /**
     * 检查字典是否正在加载
     * @param {String} dictType 字典类型
     * @returns {Boolean}
     */
    isLoading(dictType) {
      return get().loading.has(dictType)
    },

    /**
     * 从服务器加载字典数据
     * @param {String} dictType 字典类型
     * @returns {Promise<Array>} 字典数据
     */
    async loadDict(dictType) {
      if (!dictType || get().loading.has(dictType)) {
        return
      }

      set((state) => {
        state.loading.add(dictType)
      })

      try {
        // 调用统一的字典接口
        const res = await getDictByKey(dictType)
        const dictData = res[dictType] || []

        get().actions.setDict(dictType, dictData)
        return dictData
      } catch (error) {
        console.error(`加载字典 ${dictType} 失败:`, error)
        throw error
      } finally {
        set((state) => {
          state.loading.delete(dictType)
        })
      }
    },

    /**
     * 批量加载字典
     * @param {Array<String>} dictTypes 字典类型数组
     */
    async loadDicts(dictTypes) {
      const promises = dictTypes.map((dictType) => {
        return get().actions.loadDict(dictType)
      })
      try {
        await Promise.all(promises)
      } catch (error) {
        console.error('批量加载字典失败:', error)
      }
    },

    /**
     * 获取所有字典
     * @returns {Object} 所有字典数据
     */
    getAllDict() {
      const result = {}
      const dicts = get().dicts
      for (const [key, value] of dicts) {
        result[key] = value
      }
      return result
    },
  },
}))

// 创建store
const useDictsStore = import.meta.env.DEV
  ? create(devtools(store, { name: 'dict-store' })) // 开发环境可以使用redux devtools插件调试状态树
  : create(store)

// 导出字典数据hook
export const useDictsData = () => useDictsStore((state) => state.dicts)
// 导出actions hook
export const useDictsActions = () =>
  useDictsStore((state) => {
    return state.actions
  })

/**
 * 字典Hook函数 - 支持单个或多个字典类型
 * @param {...String} dictTypes 字典类型列表
 * @returns {Object} 字典数据对象
 */
export const useDict = (...dictTypes) => {
  const { getDict, loadDict } = useDictsActions()

  // 获取字典数据
  const dicts = useDictsData()

  // 加载字典数据
  useEffect(() => {
    const loadDictData = async () => {
      for (const dictType of dictTypes) {
        try {
          // 先检查store中是否已有数据
          const existingDict = getDict(dictType)
          if (!existingDict || existingDict.length === 0) {
            // 从服务器加载数据
            await loadDict(dictType)
          }
        } catch (error) {
          console.error(`加载字典 ${dictType} 失败:`, error)
        }
      }
    }

    if (dictTypes.length > 0) {
      loadDictData()
    }
  }, [dictTypes, getDict, loadDict])

  const result = {}
  for (const dictType of dictTypes) {
    result[dictType] = dicts.get(dictType) || []
  }

  return result
}

/**
 * 字典标签获取Hook
 * @param {string} dictType 字典类型
 * @param {*} value 值
 * @param {Object} options 配置选项
 * @param {string} options.labelKey 标签键名，默认'label'
 * @param {string} options.valueKey 值键名，默认'value'
 * @returns {string} 字典标签
 */
export const useDictLabel = (dictType, value, options = {}) => {
  const { labelKey = 'label', valueKey = 'value' } = options

  // 使用useDict确保字典数据存在
  const dictData = useDict(dictType)

  return useMemo(() => {
    if (!dictData || !Array.isArray(dictData)) {
      return ''
    }

    const item = dictData.find((dict) => dict[valueKey] == value)
    return item ? item[labelKey] : ''
  }, [dictData, value, labelKey, valueKey])
}

/**
 * 字典值获取Hook
 * @param {string} dictType 字典类型
 * @param {*} label 标签
 * @param {Object} options 配置选项
 * @param {string} options.labelKey 标签键名，默认'label'
 * @param {string} options.valueKey 值键名，默认'value'
 * @returns {*} 字典值
 */
export const useDictValue = (dictType, label, options = {}) => {
  const { labelKey = 'label', valueKey = 'value' } = options

  // 使用useDict确保字典数据存在
  const dictData = useDict(dictType)

  return useMemo(() => {
    if (!dictData || !Array.isArray(dictData)) {
      return null
    }

    const item = dictData.find((dict) => dict[labelKey] === label)
    return item ? item[valueKey] : null
  }, [dictData, label, labelKey, valueKey])
}

/**
 * 字典选项获取Hook
 * @param {string} dictType 字典类型
 * @returns {Array} 字典选项数组
 */
export const useDictOptions = (dictType) => {
  // 使用useDict确保字典数据存在
  const dictData = useDict(dictType)

  return useMemo(() => dictData || [], [dictData])
}

// 非hook版本兼容性函数
/**
 * 根据字典值获取标签
 * @param {string} dictType 字典类型
 * @param {string|number} value 字典值
 * @param {Object} options 配置选项
 * @returns {string} 字典标签
 */
export const getDictLabel = (dictType, value, options = {}) => {
  const { labelKey = 'label', valueKey = 'value' } = options
  const getDict = useDictsStore.getState().actions.getDict

  const dictData = getDict(dictType)

  if (!dictData || !Array.isArray(dictData)) {
    return ''
  }

  const item = dictData.find((dict) => dict[valueKey] == value)
  return item ? item[labelKey] : ''
}

/**
 * 根据字典标签获取值
 * @param {string} dictType 字典类型
 * @param {string} label 字典标签
 * @param {Object} options 配置选项
 * @returns {string|number} 字典值
 */
export const getDictValue = (dictType, label, options = {}) => {
  const { labelKey = 'label', valueKey = 'value' } = options
  const getDict = useDictsStore.getState().actions.getDict

  const dictData = getDict(dictType)

  if (!dictData || !Array.isArray(dictData)) {
    return null
  }

  const item = dictData.find((dict) => dict[labelKey] === label)
  return item ? item[valueKey] : null
}

/**
 * 获取字典选项（用于下拉框等组件）
 * @param {string} dictType 字典类型
 * @returns {Array} 字典选项数组
 */
export const getDictOptions = (dictType) => {
  const getDict = useDictsStore.getState().actions.getDict
  return getDict(dictType) || []
}

export default useDictsStore

/* 
使用示例：

// 1. 基本使用 - 单个字典
import React, { useState } from 'react'
import { useDict, useDictLabel, useDictOptions } from '@/store/dictStore'

const UserForm = () => {
  const [userStatus, setUserStatus] = useState('')
  
  // 使用字典 - 单个字典类型
  const userStatusOptions = useDict('user_status')
  
  // 使用Hook获取标签
  const statusLabel = useDictLabel('user_status', userStatus)
  
  // 获取字典选项（等同于上面的useDict）
  const statusOptions = useDictOptions('user_status')

  return (
    <div>
      <select value={userStatus} onChange={(e) => setUserStatus(e.target.value)}>
        {userStatusOptions.map(item => (
          <option key={item.value} value={item.value}>
            {item.label}
          </option>
        ))}
      </select>
      <p>当前状态标签: {statusLabel}</p>
    </div>
  )
}

// 2. 多个字典同时使用
const CompanyForm = () => {
  // 同时使用多个字典
  const { user_status, company_types, dept_list } = useDict('user_status', 'company_types', 'dept_list')
  
  return (
    <div>
      <select>
        {user_status.map(item => (
          <option key={item.value} value={item.value}>{item.label}</option>
        ))}
      </select>
      
      <select>
        {company_types.map(item => (
          <option key={item.value} value={item.value}>{item.label}</option>
        ))}
      </select>
      
      <select>
        {dept_list.map(item => (
          <option key={item.value} value={item.value}>{item.label}</option>
        ))}
      </select>
    </div>
  )
}

// 3. 批量加载字典
const Dashboard = () => {
  const { loadDicts } = useDictsActions()
  
  useEffect(() => {
    // 批量加载多个字典
    loadDicts(['user_status', 'company_types', 'dept_list'])
  }, [])
  
  return <div>Dashboard</div>
}

// 4. 自定义键名使用
const CustomForm = () => {
  const [selectedId, setSelectedId] = useState('')
  
  // 使用自定义键名
  const companyLabel = useDictLabel('company_list', selectedId, {
    labelKey: 'name',
    valueKey: 'id'
  })
  
  return (
    <div>
      <p>选中的公司: {companyLabel}</p>
    </div>
  )
}

// 5. 兼容性函数使用
const LegacyComponent = () => {
  const { getDict, getDictLabel } = useDictsActions()
  
  const handleClick = () => {
    // 直接获取字典数据
    const statusOptions = getDictOptions('user_status')
    console.log('状态选项:', statusOptions)
    
    // 直接获取标签
    const label = getDictLabel('user_status', '1')
    console.log('状态标签:', label)
  }
  
  return <button onClick={handleClick}>获取字典信息</button>
}
*/
