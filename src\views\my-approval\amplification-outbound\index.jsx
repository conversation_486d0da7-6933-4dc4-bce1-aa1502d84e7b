import { getCellApplyInfoDetail } from '@/api/myApproval'
import Breadcrumb from '@/components/Breadcrumb'
import Content, { ContentTitle } from '@/layout/components/Content'
import { Button, Descriptions, Form, Input, Spin, Table, message } from 'antd'
import { useCallback, useEffect, useState } from 'react'
import { useNavigate, useParams } from 'react-router'

const AmplificationOutbound = () => {
  const { id } = useParams()
  const navigate = useNavigate()
  const [detail, setDetail] = useState(null)
  const [loading, setLoading] = useState(true)
  const [actionLoading, setActionLoading] = useState(false)
  const [form] = Form.useForm()

  const fetchDetail = useCallback(async () => {
    setLoading(true)
    try {
      const res = await getCellApplyInfoDetail(id)
      setDetail(res)
      // 设置表单默认值
      form.setFieldsValue({
        outboundTime: new Date().toISOString().split('T')[0],
        outboundPerson: '当前用户', // 这里应该从用户信息中获取
      })
    } catch {
      message.error('获取详情失败')
      navigate(-1)
    } finally {
      setLoading(false)
    }
  }, [id, navigate, form])

  useEffect(() => {
    fetchDetail()
  }, [fetchDetail])

  const handleLockOutbound = useCallback(() => {
    setActionLoading(true)
    // 这里应该调用锁定出库信息的API
    setTimeout(() => {
      message.success('出库信息已锁定')
      setActionLoading(false)
      navigate(-1)
    }, 1000)
  }, [navigate])

  const handlePositionRecommend = useCallback(() => {
    message.info('位置推荐功能待实现')
  }, [])

  // 细胞位置表格列配置
  const positionColumns = [
    {
      title: '细胞中文名称',
      dataIndex: 'cellNameCn',
      key: 'cellNameCn',
    },
    {
      title: '细胞英文名称',
      dataIndex: 'cellNameEn',
      key: 'cellNameEn',
    },
    {
      title: '库类别',
      dataIndex: 'storageType',
      key: 'storageType',
    },
    {
      title: '液氮罐',
      dataIndex: 'nitrogenTank',
      key: 'nitrogenTank',
    },
    {
      title: '提篮',
      dataIndex: 'basket',
      key: 'basket',
    },
    {
      title: '层数',
      dataIndex: 'layer',
      key: 'layer',
    },
    {
      title: '孔位',
      dataIndex: 'position',
      key: 'position',
    },
    {
      title: '管数',
      dataIndex: 'tubeCount',
      key: 'tubeCount',
    },
    {
      title: '操作',
      key: 'action',
      render: () => <a onClick={() => message.info('修改功能待实现')}>修改</a>,
    },
  ]

  // 模拟细胞位置数据
  const positionData = [
    {
      key: '1',
      cellNameCn: detail?.cellNameCn || '',
      cellNameEn: detail?.cellNameEn || '',
      storageType: '液氮库',
      nitrogenTank: 'LN-001',
      basket: 'B-01',
      layer: '3',
      position: 'A1',
      tubeCount: 5,
    },
  ]

  const validateOutboundCount = (_, value) => {
    if (!value) {
      return Promise.reject(new Error('出库管数不能为空'))
    }
    const stockCount = detail?.stockTubeCount || 0
    if (value > stockCount) {
      return Promise.reject(
        new Error(`出库管数不能超过库存管数(${stockCount})`),
      )
    }
    return Promise.resolve()
  }

  const cellInfoItems = [
    {
      key: 'cellCode',
      label: '细胞中心编号',
      children: detail?.cellCode,
    },
    {
      key: 'cellNameCn',
      label: '细胞中文名称',
      children: detail?.cellNameCn,
    },
    {
      key: 'cellNameEn',
      label: '细胞英文名称',
      children: detail?.cellNameEn,
    },
    {
      key: 'cellSpecies',
      label: '细胞种属',
      children: detail?.cellSpecies === 1 ? '人' : '小鼠',
    },
    {
      key: 'sourceOrgan',
      label: '细胞来源',
      children: detail?.sourceOrgan,
    },
    {
      key: 'originalBatchNo',
      label: '原始批次号',
      children: detail?.originalBatchNo || '-',
    },
    {
      key: 'frozenBatchNo',
      label: '冻存批次号',
      children: detail?.frozenBatchNo || '-',
    },
    {
      key: 'cellType',
      label: '细胞类型',
      children: detail?.cellType,
    },
    {
      key: 'frozenTubeCount',
      label: '冻存管数',
      children: detail?.frozenTubeCount || '-',
    },
    {
      key: 'recoveryTubeCount',
      label: '复苏管数',
      children: detail?.recoveryTubeCount || '-',
    },
    {
      key: 'stockTubeCount',
      label: '库存管数',
      children: detail?.stockTubeCount || '-',
    },
    {
      key: 'cellGeneration',
      label: '细胞代次',
      children: detail?.cellGeneration,
    },
    {
      key: 'medium',
      label: '培养基',
      children: detail?.medium,
    },
    {
      key: 'cultureCondition',
      label: '培养条件',
      children: detail?.cultureCondition,
    },
    {
      key: 'acceptRecoveryTime',
      label: '接收/复苏时间',
      children: detail?.acceptRecoveryTime,
    },
    {
      key: 'remarks',
      label: '备注',
      children: detail?.remarks || '-',
      span: 2,
    },
  ]

  return (
    <div className="flex h-full flex-col overflow-hidden">
      <Breadcrumb showBackArrow>
        <Button
          type="primary"
          onClick={handleLockOutbound}
          loading={actionLoading}>
          锁定出库信息
        </Button>
      </Breadcrumb>

      <div className="flex flex-1 flex-col overflow-hidden">
        <div className="bg-white p-4 shadow-sm">
          <h2 className="text-lg font-medium text-gray-900">
            入库订单号: {detail?.applyNo || ''}
          </h2>
        </div>

        <div className="flex-1 overflow-auto bg-white">
          <Spin spinning={loading}>
            {detail && (
              <div className="p-6">
                <ContentTitle title="细胞信息" />
                <Content>
                  <Descriptions
                    items={cellInfoItems}
                    column={2}
                    bordered
                    size="small"
                    className="mb-8"
                  />
                </Content>

                <ContentTitle title="出库信息" />
                <Content>
                  <Form
                    form={form}
                    layout="vertical"
                    className="mb-6">
                    <div className="grid grid-cols-3 gap-6">
                      <Form.Item
                        label="出库管数"
                        name="outboundCount"
                        rules={[
                          { required: true, message: '请输入出库管数' },
                          { validator: validateOutboundCount },
                        ]}>
                        <Input placeholder="请输入出库管数" />
                      </Form.Item>
                      <Form.Item
                        label="出库时间"
                        name="outboundTime">
                        <Input disabled />
                      </Form.Item>
                      <Form.Item
                        label="出库人"
                        name="outboundPerson">
                        <Input disabled />
                      </Form.Item>
                    </div>
                  </Form>

                  <div className="mb-4 flex items-center justify-between">
                    <h3 className="text-base font-medium">细胞位置</h3>
                    <Button
                      type="link"
                      onClick={handlePositionRecommend}>
                      位置推荐
                    </Button>
                  </div>

                  <Table
                    columns={positionColumns}
                    dataSource={positionData}
                    pagination={false}
                    size="small"
                    bordered
                  />
                </Content>
              </div>
            )}
          </Spin>
        </div>
      </div>
    </div>
  )
}

export default AmplificationOutbound
