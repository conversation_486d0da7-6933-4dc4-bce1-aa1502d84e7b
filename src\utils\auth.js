import useUserStore from '@/store/userStore'

/**
 * 检查用户是否拥有某个权限
 * @param {Array} permissions - 用户拥有的权限列表
 * @param {String} permission - 需要检查的权限
 * @return {Boolean} - 用户是否拥有该权限
 */
export function authPermission(permissions, permission) {
  const all_permission = '*:*:*'
  if (permission && permission.length > 0) {
    return permissions.some((v) => v === all_permission || v === permission)
  } else {
    return false
  }
}

/**
 * 检查用户是否拥有某个角色
 * @param {Array} roles - 用户拥有的角色列表
 * @param {String} role - 需要检查的角色
 * @return {Boolean} - 用户是否拥有该角色
 */
export function authRole(roles, role) {
  const super_admin = 'super-admin'
  if (role && role.length > 0) {
    return roles.some((v) => v === super_admin || v === role)
  } else {
    return false
  }
}

export default {
  /**
   * 验证用户是否具备某权限
   * @param {String} permission - 需要验证的权限
   * @return {Boolean} - 用户是否具备该权限
   */
  hasPermi(permission) {
    const permissions = useUserStore.getState().userPermissions
    return authPermission(permissions, permission)
  },
  /**
   * 验证用户是否含有指定权限，只需包含其中一个
   * @param {Array} permissions - 需要验证的权限列表
   * @return {Boolean} - 用户是否含有其中任一权限
   */
  hasPermiOr(permissions) {
    const userPermissions = useUserStore.getState().userPermissions
    return permissions.some((item) => authPermission(userPermissions, item))
  },
  /**
   * 验证用户是否含有指定权限，必须全部拥有
   * @param {Array} permissions - 需要验证的权限列表
   * @return {Boolean} - 用户是否含有全部权限
   */
  hasPermiAnd(permissions) {
    const userPermissions = useUserStore.getState().userPermissions
    return permissions.every((item) => authPermission(userPermissions, item))
  },
  /**
   * 验证用户是否具备某角色
   * @param {String} role - 需要验证的角色
   * @return {Boolean} - 用户是否具备该角色
   */
  hasRole(role) {
    const roles = useUserStore.getState().userRoles
    return authRole(roles, role)
  },
  /**
   * 验证用户是否含有指定角色，只需包含其中一个
   * @param {Array} roles - 需要验证的角色列表
   * @return {Boolean} - 用户是否含有其中任一角色
   */
  hasRoleOr(roles) {
    const userRoles = useUserStore.getState().userRoles
    return roles.some((item) => authRole(userRoles, item))
  },
  /**
   * 验证用户是否含有指定角色，必须全部拥有
   * @param {Array} roles - 需要验证的角色列表
   * @return {Boolean} - 用户是否含有全部角色
   */
  hasRoleAnd(roles) {
    const userRoles = useUserStore.getState().userRoles
    return roles.every((item) => authRole(userRoles, item))
  },
}
