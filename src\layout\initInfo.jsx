import PageSpin from '@/components/PageSpin'
import { useLogout } from '@/store/userStore'
import { Button, Result } from 'antd'
import { useAccountInfo } from './hooks'

/**
 * 初始化账号信息 （角色，权限等）
 */
function InitInfo({ children }) {
  const { accountInfo, errorMessage } = useAccountInfo()
  const logout = useLogout()

  // 初始化系统信息存在错误
  if (errorMessage)
    return (
      <Result
        className="tw-pt-48"
        status="error"
        title="获取系统相关信息失败,请刷新界面、重新登录或联系管理员!"
        subTitle={<p style={{ color: 'red' }}>{errorMessage}</p>}
        extra={
          <Button
            type="primary"
            key="console"
            onClick={logout}>
            重新登录或刷新界面
          </Button>
        }
      />
    )

  // 没有信息则加载中
  if (!accountInfo)
    return (
      <PageSpin tip="加载系统数据">
        <div
          style={{
            padding: 50,
            borderRadius: 4,
          }}
        />
      </PageSpin>
    )

  return children
}

export default InitInfo
