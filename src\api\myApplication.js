import { get, post } from '@/utils/request/alova'

/**
 * 申请复检
 * @param {string} applyBid 申请记录主键
 * @param {string} analysisItem 检测项目（多个项目逗号分隔）：1STR、2同工酶、3PCR种属鉴定、4DNA Barcoding、5支原体培养法、6支原体PCR检测法、7支原体DNA染色法、8细菌真菌接种法
 * @returns
 */
export function applyRecheck(applyBid, analysisItem) {
  return get('/cell/apply/applyRecheck', { applyBid, analysisItem })
}

/**
 * 放弃入库
 * @param {string} applyBid 申请记录主键
 * @returns
 */
export function abandoningStorage(applyBid) {
  return get('/cell/apply/abandoningStorage', { applyBid })
}

/**
 * 质检申请提交入库
 * @param {Object} data 质检申请提交入库DTO
 * @param {string} data.applyBid 质检申请
 * @param {string} data.cellBid 细胞主键
 * @param {string} data.cellCode 细胞中心编号
 * @param {string} data.cellNameCn 细胞中文名称
 * @param {string} data.cellNameEn 细胞英文名称
 * @param {number} data.cellSpecies 细胞种属：1-人，2-小鼠
 * @param {string} data.cultureCondition 培养条件
 * @param {string} data.medium 培养基
 * @param {string} data.stockInBid 入库订单主键
 * @param {Object} data.stockInItem 单条入库信息DTO
 * @returns
 */
export function stockInApply(data) {
  return post('/cell/apply/stockInApply', data)
}

/**
 * 提交质控申请/重新发起
 * @param {Object} data 提交质检申请DTO
 * @param {string} data.acceptRecoveryTime 接收恢复时间
 * @param {string} data.cellCode 细胞中心编号
 * @param {string} data.cellGeneration 细胞代次
 * @param {string} data.cellNameCn 细胞中文名称
 * @param {string} data.cellNameEn 细胞英文名称
 * @param {number} data.cellSpecies 细胞种属：1-人，2-小鼠
 * @param {string} data.cellType 细胞类型：贴壁或悬浮
 * @param {string} data.cultureCondition 培养条件
 * @param {string} data.lastApplyBid 上次质检任务id（重新发起时传参数）
 * @param {string} data.medium 培养基
 * @param {string} data.remarks 备注
 * @param {string} data.sourceOrgan 细胞来源
 * @param {string} data.analysisItem 检测项目（多个项目逗号分隔）：1STR、2同工酶、3PCR种属鉴定、4DNA Barcoding、5支原体培养法、6支原体PCR检测法、7支原体DNA染色法、8细菌真菌接种法
 * @returns
 */
export function saveDetectionApply(data) {
  return post('/cell/apply/saveDetectionApply', data)
}
