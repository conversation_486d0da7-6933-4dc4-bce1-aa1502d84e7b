import { CaretRightOutlined, DownOutlined, UpOutlined } from '@ant-design/icons'
import { Button, Card, Collapse, Table, Timeline } from 'antd'
import { useState } from 'react'
import QualityTestCard from './QualityTestCard'

const { Panel } = Collapse

function QualityInfoSection({ data }) {
  const [expandedCards, setExpandedCards] = useState({})

  // 检测项目表格列
  const testItemColumns = [
    {
      title: '检测项目',
      dataIndex: 'testItem',
      key: 'testItem',
    },
    {
      title: '检测结果',
      dataIndex: 'testResult',
      key: 'testResult',
    },
  ]

  // 切换卡片展开状态
  const toggleCardExpanded = (taskId, testId) => {
    const key = `${taskId}-${testId}`
    setExpandedCards((prev) => ({
      ...prev,
      [key]: !prev[key],
    }))
  }

  // 渲染质检历史时间轴
  const renderQualityHistory = () => {
    if (!data?.qualityHistory || data.qualityHistory.length === 0) {
      return <div>暂无质检历史</div>
    }

    const timelineItems = data.qualityHistory.map((task, index) => {
      const getStatusColor = (status) => {
        switch (status) {
          case '任务进行中':
            return 'blue'
          case '已完成':
            return 'green'
          default:
            return 'gray'
        }
      }

      const getStatusLabel = (task) => {
        if (task.status === '已完成') {
          return `${task.status} - ${task.completionTime} - ${task.tester}`
        }
        return task.status
      }

      return {
        color: getStatusColor(task.status),
        children: (
          <div key={task.id || index}>
            <div style={{ marginBottom: 16, fontWeight: 'bold' }}>
              {getStatusLabel(task)}
            </div>
            <div style={{ display: 'flex', flexDirection: 'column', gap: 12 }}>
              {task.testItems?.map((testItem, testIndex) => {
                const cardKey = `${task.id || index}-${testItem.id || testIndex}`
                const isExpanded = expandedCards[cardKey]

                return (
                  <Card
                    key={testItem.id || testIndex}
                    size="small"
                    style={{ marginLeft: 0 }}
                    title={
                      <div
                        style={{
                          display: 'flex',
                          justifyContent: 'space-between',
                          alignItems: 'center',
                        }}>
                        <div>
                          <span style={{ marginRight: 16 }}>
                            检测项目: {testItem.testItem}
                          </span>
                          <span style={{ marginRight: 16 }}>
                            检测结果: {testItem.testResult}
                          </span>
                          <span style={{ marginRight: 16 }}>
                            检测时间: {testItem.testTime}
                          </span>
                          <span>检测人: {testItem.tester}</span>
                        </div>
                        <Button
                          type="text"
                          size="small"
                          icon={isExpanded ? <UpOutlined /> : <DownOutlined />}
                          onClick={() =>
                            toggleCardExpanded(
                              task.id || index,
                              testItem.id || testIndex,
                            )
                          }>
                          {isExpanded ? '收起' : '展开'}
                        </Button>
                      </div>
                    }>
                    {isExpanded && (
                      <QualityTestCard
                        testItem={testItem.testItem}
                        data={testItem.detailData}
                      />
                    )}
                  </Card>
                )
              })}
            </div>
          </div>
        ),
      }
    })

    return <Timeline items={timelineItems} />
  }

  return (
    <Collapse
      defaultActiveKey={['qualityInfo']}
      expandIcon={({ isActive }) => (
        <CaretRightOutlined rotate={isActive ? 90 : 0} />
      )}
      expandIconPosition="end">
      <Panel
        header={
          <span style={{ fontSize: '16px', fontWeight: 'bold' }}>质检信息</span>
        }
        key="qualityInfo">
        {/* 检测项目表格 */}
        <div style={{ marginBottom: 24 }}>
          <Table
            columns={testItemColumns}
            dataSource={data?.testItems || []}
            pagination={false}
            size="small"
            rowKey={(record, index) => record.id || index}
          />
        </div>

        {/* 质检历史时间轴 */}
        <div>{renderQualityHistory()}</div>
      </Panel>
    </Collapse>
  )
}

export default QualityInfoSection
